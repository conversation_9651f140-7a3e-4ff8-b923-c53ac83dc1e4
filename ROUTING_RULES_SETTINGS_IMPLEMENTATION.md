# Реализация системы настроек и управления routing rules

## Обзор функциональности

### **Создана система настроек с таблицей settings:**
- ✅ Таблица settings (ключ-значение) для хранения настроек
- ✅ Поле use_common_routing в таблице users
- ✅ Модель Setting с кэшированием
- ✅ Чекбокс "Не использовать VPN для сайтов РФ" в интерфейсе
- ✅ API для обновления настройки пользователя

## Структура базы данных

### **1. Таблица settings**
```sql
CREATE TABLE settings (
    id BIGINT PRIMARY KEY,
    key VARCHAR(255) UNIQUE,
    value LONGTEXT,
    description VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX(key)
);
```

### **2. Поле в таблице users**
```sql
ALTER TABLE users ADD COLUMN use_common_routing BOOLEAN DEFAULT TRUE;
```

### **3. Первоначальная запись**
```sql
INSERT INTO settings (key, value, description) VALUES (
    'common_routing_rules',
    '', -- Значение будет вставлено вручную
    'Common routing rules for VPN clients (base64 encoded)'
);
```

## Модель Setting

### **Файл:** `app/Models/Setting.php`

### **Основные методы:**
```php
// Получить значение настройки (с кэшированием)
Setting::get('common_routing_rules', '');

// Установить значение настройки
Setting::set('common_routing_rules', $value, 'Description');

// Проверить существование настройки
Setting::has('common_routing_rules');

// Удалить настройку
Setting::remove('common_routing_rules');

// Получить все настройки
Setting::all();

// Очистить кэш
Setting::clearCache();
```

### **Кэширование:**
- Автоматическое кэширование на 1 час
- Автоматическая очистка кэша при изменении
- Ключи кэша: `setting.{key}`

## Логика routing rules

### **В SubscriptionController::getSubscriptionHeaders():**
```php
// Определяем routing rules на основе настройки пользователя
$routingRules = '';
if ($uuid) {
    $user = User::findByUuid($uuid);
    if ($user && $user->use_common_routing) {
        $routingRules = Setting::get('common_routing_rules', '');
    }
}

$headers = [
    // ... другие заголовки
    'Routing' => $routingRules,
];
```

### **Логика работы:**
- **Если `use_common_routing = true`** → подставляется значение из `common_routing_rules`
- **Если `use_common_routing = false`** → подставляется пустая строка
- **Если настройка не найдена** → подставляется пустая строка

## Пользовательский интерфейс

### **Чекбокс в subscription-modern-simple.blade.php:**
```html
<!-- Routing Preference Checkbox -->
<div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
    <div class="flex-1">
        <label for="routingCheckbox" class="text-sm font-medium text-gray-200 cursor-pointer">
            Не использовать VPN для сайтов РФ
        </label>
        <p class="text-xs text-gray-400 mt-1">
            Российские сайты будут открываться напрямую
        </p>
    </div>
    <div class="flex-shrink-0 ml-3">
        <input
            type="checkbox"
            id="routingCheckbox"
            class="w-5 h-5 text-blue-600 bg-gray-600 border-gray-500 rounded focus:ring-blue-500 focus:ring-2"
            {{ $data['use_common_routing'] ? 'checked' : '' }}
        >
    </div>
</div>
```

### **JavaScript обработчик:**
```javascript
const routingCheckbox = document.getElementById('routingCheckbox');
routingCheckbox.addEventListener('change', async function() {
    const isChecked = this.checked;
    
    try {
        this.disabled = true;
        
        const response = await fetch(`/subs/{{ $data['uuid'] }}/routing-preference`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                use_common_routing: isChecked
            })
        });

        const data = await response.json();

        if (data.success) {
            showToast(data.message, 'success');
        } else {
            this.checked = !isChecked; // Откат при ошибке
            showToast(data.message, 'error');
        }
    } catch (error) {
        this.checked = !isChecked; // Откат при ошибке
        showToast('Ошибка при обновлении настройки', 'error');
    } finally {
        this.disabled = false;
    }
});
```

## API endpoint

### **Маршрут:**
```php
Route::post('/subs/{uuid}/routing-preference', [SubscriptionController::class, 'updateRoutingPreference'])
    ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');
```

### **Метод SubscriptionController::updateRoutingPreference():**
```php
public function updateRoutingPreference(Request $request, string $uuid): JsonResponse
{
    // Валидация UUID
    if (!$this->helperService->isValidUuid($uuid)) {
        return response()->json(['success' => false, 'message' => 'Invalid UUID format'], 400);
    }

    // Поиск пользователя
    $user = User::findByUuid($uuid);
    if (!$user) {
        return response()->json(['success' => false, 'message' => 'User not found'], 404);
    }

    // Валидация данных
    $request->validate(['use_common_routing' => 'required|boolean']);

    // Обновление настройки
    $user->update(['use_common_routing' => $request->use_common_routing]);

    // Логирование
    Log::info('User routing preference updated', [
        'user_id' => $user->id,
        'uuid' => $uuid,
        'use_common_routing' => $request->use_common_routing,
    ]);

    return response()->json([
        'success' => true,
        'message' => 'Настройка обновлена',
        'data' => ['use_common_routing' => $user->use_common_routing],
    ]);
}
```

## Примеры использования

### **1. Установка routing rules в БД:**
```sql
UPDATE settings 
SET value = 'eyJkb21haW5TdHJhdGVneSI6IklQT25EZW1hbmQi...' 
WHERE key = 'common_routing_rules';
```

### **2. Проверка настройки пользователя:**
```php
$user = User::findByUuid($uuid);
if ($user->use_common_routing) {
    echo "Пользователь использует общие routing rules";
} else {
    echo "Пользователь не использует routing rules";
}
```

### **3. Получение routing rules:**
```php
$routingRules = Setting::get('common_routing_rules', '');
if ($routingRules) {
    echo "Routing rules установлены";
} else {
    echo "Routing rules не установлены";
}
```

## Логирование

### **При обновлении настройки пользователя:**
```php
Log::info('User routing preference updated', [
    'user_id' => $user->id,
    'uuid' => $uuid,
    'tg_id' => $user->tg_id,
    'use_common_routing' => $request->use_common_routing,
    'ip' => $request->ip(),
]);
```

## Безопасность

### **Валидация:**
- ✅ Проверка формата UUID
- ✅ Проверка существования пользователя
- ✅ Валидация boolean значения
- ✅ CSRF защита

### **Обработка ошибок:**
- ✅ Откат состояния чекбокса при ошибке
- ✅ Toast уведомления об ошибках
- ✅ Логирование всех операций

## Миграции

### **Созданные файлы:**
1. `database/migrations/2024_12_30_000006_create_settings_table.php`
2. `database/migrations/2024_12_30_000007_add_use_common_routing_to_users_table.php`

### **Запуск:**
```bash
php artisan migrate
```

## Обновленные файлы

### **Модели:**
- `app/Models/Setting.php` - новая модель
- `app/Models/User.php` - добавлено поле use_common_routing

### **Контроллеры:**
- `app/Http/Controllers/SubscriptionController.php` - обновлена логика routing rules

### **Маршруты:**
- `routes/web.php` - добавлен маршрут для API

### **Шаблоны:**
- `resources/views/subs/subscription-modern-simple.blade.php` - добавлен чекбокс

## Результат

### **Функциональность:**
- ✅ Настройки хранятся в БД (таблица settings)
- ✅ Пользователи могут включать/выключать routing rules
- ✅ Routing rules подставляются в заголовки подписки
- ✅ Удобный интерфейс с чекбоксом
- ✅ Мгновенное обновление через AJAX

### **Преимущества:**
- ✅ Централизованное хранение настроек
- ✅ Кэширование для производительности
- ✅ Гибкость настройки для каждого пользователя
- ✅ Простое управление через интерфейс

Система настроек и управления routing rules полностью реализована!
