# Реализация поддержки SOCKS5 прокси

## Что реализовано

### **1. Конфигурация прокси**

#### **config/app.php:**
```php
/*
|--------------------------------------------------------------------------
| HTTP Proxy Configuration
|--------------------------------------------------------------------------
|
| SOCKS5 proxy configuration for HTTP clients.
|
*/

'socks5_proxy' => env('SOCKS5_PROXY', null),
```

#### **.env файл:**
```env
# SOCKS5 Proxy for HTTP clients (optional)
# SOCKS5_PROXY="socks5://127.0.0.1:1080"
```

### **2. HttpClientService - универсальный сервис**

#### **app/Services/HttpClientService.php:**
```php
<?php

namespace App\Services;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HttpClientService
{
    /**
     * Create HTTP client with proxy support.
     */
    public static function create(array $options = []): PendingRequest
    {
        // Add SOCKS5 proxy if configured
        $socks5Proxy = config('app.socks5_proxy');
        if ($socks5Proxy) {
            $options['proxy'] = [
                'http' => $socks5Proxy,
                'https' => $socks5Proxy,
            ];
            Log::debug("Using SOCKS5 proxy for HTTP client: {$socks5Proxy}");
        }

        return Http::withOptions($options);
    }

    /**
     * Create HTTP client with proxy and common options.
     */
    public static function createWithDefaults(array $additionalOptions = []): PendingRequest
    {
        $defaultOptions = [
            'timeout' => 30,
            'connect_timeout' => 10,
            'verify' => false,
        ];

        $options = array_merge($defaultOptions, $additionalOptions);

        return self::create($options);
    }

    /**
     * Create HTTP client for API calls with proxy support.
     */
    public static function createForApi(array $headers = [], array $options = []): PendingRequest
    {
        $defaultHeaders = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'User-Agent' => 'Laravel-App/1.0',
        ];

        $headers = array_merge($defaultHeaders, $headers);

        return self::createWithDefaults($options)
            ->withHeaders($headers)
            ->retry(2, 500);
    }

    /**
     * Check if proxy is configured.
     */
    public static function isProxyConfigured(): bool
    {
        return !empty(config('app.socks5_proxy'));
    }

    /**
     * Get proxy configuration.
     */
    public static function getProxyConfig(): ?string
    {
        return config('app.socks5_proxy');
    }
}
```

### **3. Обновленный XuiApiService**

#### **Было:**
```php
return Http::withHeaders([
        'X-requested-with' => 'XMLHttpRequest',
    ])
    ->withOptions(['cookies' => $cookieJar])
    ->withoutVerifying()
    ->timeout(30)
    ->connectTimeout(10)
    ->retry(2, 500)
    ->asForm()
    ->baseUrl($server->url);
```

#### **Стало:**
```php
// Prepare options with cookies
$options = ['cookies' => $cookieJar];

// Create HTTP client with proxy support
return HttpClientService::create($options)
    ->withHeaders([
        'X-requested-with' => 'XMLHttpRequest',
    ])
    ->withoutVerifying()
    ->timeout(30)
    ->connectTimeout(10)
    ->retry(2, 500)
    ->asForm()
    ->baseUrl($server->url);
```

## Использование

### **1. Настройка прокси в .env**
```env
# Для локального SOCKS5 прокси
SOCKS5_PROXY="socks5://127.0.0.1:1080"

# Для удаленного SOCKS5 прокси
SOCKS5_PROXY="socks5://username:<EMAIL>:1080"

# Для отключения прокси (по умолчанию)
# SOCKS5_PROXY=
```

### **2. Использование HttpClientService в новом коде**

#### **Простой HTTP клиент:**
```php
use App\Services\HttpClientService;

// Создать клиент с прокси поддержкой
$client = HttpClientService::create();
$response = $client->get('https://api.example.com/data');
```

#### **HTTP клиент с настройками по умолчанию:**
```php
// Клиент с таймаутами и без проверки SSL
$client = HttpClientService::createWithDefaults();
$response = $client->post('https://api.example.com/endpoint', $data);
```

#### **API клиент с заголовками:**
```php
// Клиент для API с JSON заголовками
$client = HttpClientService::createForApi([
    'Authorization' => 'Bearer ' . $token,
]);
$response = $client->get('https://api.example.com/user');
```

### **3. Проверка конфигурации прокси**
```php
use App\Services\HttpClientService;

// Проверить настроен ли прокси
if (HttpClientService::isProxyConfigured()) {
    $proxy = HttpClientService::getProxyConfig();
    Log::info("Using proxy: {$proxy}");
} else {
    Log::info("No proxy configured");
}
```

## Форматы SOCKS5 прокси

### **1. Локальный прокси:**
```env
SOCKS5_PROXY="socks5://127.0.0.1:1080"
```

### **2. Прокси с аутентификацией:**
```env
SOCKS5_PROXY="socks5://username:<EMAIL>:1080"
```

### **3. Прокси без аутентификации:**
```env
SOCKS5_PROXY="socks5://proxy.example.com:1080"
```

### **4. HTTP прокси (альтернатива):**
```env
SOCKS5_PROXY="http://proxy.example.com:8080"
```

## Логирование

### **При использовании прокси:**
```
[DEBUG] Using SOCKS5 proxy for HTTP client: socks5://127.0.0.1:1080
```

### **Без прокси:**
```
# Никаких логов не создается
```

## Тестирование

### **1. Проверка без прокси:**
```bash
# Убедитесь что SOCKS5_PROXY не установлен
grep SOCKS5_PROXY .env

# Запустите команду обновления
php artisan subscription:refresh
```

### **2. Проверка с прокси:**
```bash
# Установите прокси в .env
echo 'SOCKS5_PROXY="socks5://127.0.0.1:1080"' >> .env

# Очистите кэш конфигурации
php artisan config:clear

# Запустите команду обновления
php artisan subscription:refresh
```

### **3. Проверка в коде:**
```php
// В контроллере или команде
use App\Services\HttpClientService;

// Проверить конфигурацию
dd([
    'proxy_configured' => HttpClientService::isProxyConfigured(),
    'proxy_config' => HttpClientService::getProxyConfig(),
    'config_value' => config('app.socks5_proxy'),
]);
```

## Поддерживаемые протоколы

### **SOCKS5 (рекомендуется):**
```env
SOCKS5_PROXY="socks5://127.0.0.1:1080"
```

### **SOCKS4:**
```env
SOCKS5_PROXY="socks4://127.0.0.1:1080"
```

### **HTTP/HTTPS:**
```env
SOCKS5_PROXY="http://127.0.0.1:8080"
SOCKS5_PROXY="https://127.0.0.1:8080"
```

## Обновленные компоненты

### **1. Конфигурация:**
- `config/app.php` - добавлена секция HTTP Proxy Configuration
- `.env` - добавлен пример SOCKS5_PROXY

### **2. Сервисы:**
- `app/Services/HttpClientService.php` - новый универсальный сервис
- `app/Services/XuiApiService.php` - обновлен для использования HttpClientService

### **3. Логирование:**
- Добавлено логирование использования прокси
- Debug уровень для избежания спама в логах

## Преимущества

### **1. Централизованная конфигурация:**
- Один параметр SOCKS5_PROXY для всех HTTP клиентов
- Легко включить/выключить прокси

### **2. Универсальность:**
- HttpClientService можно использовать в любом месте приложения
- Автоматическая поддержка прокси во всех новых HTTP клиентах

### **3. Обратная совместимость:**
- Если SOCKS5_PROXY не установлен, работает как раньше
- Никаких изменений в существующем коде не требуется

### **4. Гибкость:**
- Поддержка разных типов прокси (SOCKS5, SOCKS4, HTTP)
- Поддержка аутентификации
- Настраиваемые опции для разных случаев использования

## Возможные проблемы

### **1. Прокси недоступен:**
```
Connection timeout или Connection refused
```
**Решение:** Проверить что прокси сервер запущен и доступен

### **2. Неправильный формат прокси:**
```
Invalid proxy format
```
**Решение:** Использовать правильный формат: `socks5://host:port`

### **3. Аутентификация не работает:**
```
Proxy authentication failed
```
**Решение:** Проверить username:password в URL прокси

### **4. SSL ошибки через прокси:**
```
SSL certificate verification failed
```
**Решение:** Использовать `->withoutVerifying()` или настроить SSL правильно

Теперь все HTTP клиенты в приложении поддерживают SOCKS5 прокси!
