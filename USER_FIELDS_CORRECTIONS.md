# Исправления логики полей пользователя

## Проблемы и исправления

### **1. Логика расчета expiry_time**

#### **Было (неправильно):**
```php
// Default: demo: 1 day, regular: 30 days
$expiryDays = $isDemo ? 1 : 30;
$expiryTime = now()->addDays($expiryDays);
```

#### **Стало (правильно):**
```php
// Calculate expiry_time based on priority: expiration > demo flag > default
if ($expiration) {
    // If expiration is provided, use it (ignore demo flag)
    $expiryTime = $expiration > 9999999999
        ? \Carbon\Carbon::createFromTimestampMs($expiration)
        : \Carbon\Carbon::createFromTimestamp($expiration);
} else {
    // If no expiration provided, use demo flag or default
    if ($isDemo) {
        // Demo: 1 day
        $expiryTime = now()->addDay();
    } else {
        // Regular: 1 week
        $expiryTime = now()->addWeek();
    }
}
```

#### **Приоритет логики:**
1. **expiration параметр** - если передан, используется (игнорирует demo флаг)
2. **demo флаг** - если true и expiration не передан, то 1 день
3. **по умолчанию** - если demo false и expiration не передан, то 1 неделя

### **2. Поля XuiClient из User модели**

#### **Было (неправильно):**
```php
'tg_id' => $tgId, // Использовалась переменная
'sub_id' => "unlimited_{$tgId}", // Использовалась переменная
'enable' => true, // Статичное значение
```

#### **Стало (правильно):**
```php
'tg_id' => $user->tg_id, // Из User модели
'sub_id' => "unlimited_{$user->tg_id}", // Из User модели
'enable' => !$user->expired && !$user->disabled_at, // Рассчитывается из User
```

### **3. Вызов createClient**

#### **Было (неправильно):**
```php
$success = $xuiApiService->createClient($server, $inbound, $user, $clientEmail, $comment, $isDemo, $user->demo_until);
```

#### **Стало (правильно):**
```php
$success = $xuiApiService->createClient($server, $inbound, $user, $clientEmail, $user->comment, $isDemo, $user->expiry_time);
```

### **4. XuiApiService clientData**

#### **Было (неправильно):**
```php
'totalGB' => $isDemo ? 1073741824 : 0, // Использовался флаг
'expiryTime' => $user->expiry_time ? $user->expiry_time->timestamp * 1000 : $expirationTimestamp,
'tgId' => '', // Пустое значение
```

#### **Стало (правильно):**
```php
'totalGB' => $user->total_gb, // Из User модели
'expiryTime' => $expirationTimestamp, // Рассчитывается из User
'tgId' => $user->tg_id, // Из User модели
```

## Полные исправления

### **1. API Контроллер**
**Файл:** `app/Http/Controllers/Api/SubscriptionApiController.php`

#### **Правильная логика expiry_time:**
```php
// Calculate expiry_time based on priority: expiration > demo flag > default
$expiryTime = null;
if ($expiration) {
    // If expiration is provided, use it (ignore demo flag)
    $expiryTime = $expiration > 9999999999
        ? \Carbon\Carbon::createFromTimestampMs($expiration)
        : \Carbon\Carbon::createFromTimestamp($expiration);
} else {
    // If no expiration provided, use demo flag or default
    if ($isDemo) {
        // Demo: 1 day
        $expiryTime = now()->addDay();
    } else {
        // Regular: 1 week
        $expiryTime = now()->addWeek();
    }
}
```

#### **Правильное создание XuiClient:**
```php
$xuiClient = XuiClient::create([
    'user_id' => $user->id,
    'xui_server_id' => $server->id,
    'xui_inbound_id' => $inbound->id,
    'client_id' => $user->uuid,
    'email' => $clientEmail,
    'enable' => !$user->expired && !$user->disabled_at,
    'disabled_at' => $user->disabled_at,
    'tg_id' => $user->tg_id, // ✅ Из User модели
    'sub_id' => "unlimited_{$user->tg_id}", // ✅ Из User модели
    'comment' => $user->comment, // ✅ Из User модели
    'expiry_time' => $user->expiry_time, // ✅ Из User модели
    'expired' => $user->expired, // ✅ Из User модели
    'total_gb' => $user->total_gb, // ✅ Из User модели
    'used_gb' => $user->used_gb, // ✅ Из User модели
    'up_traffic' => $user->up_traffic, // ✅ Из User модели
    'down_traffic' => $user->down_traffic, // ✅ Из User модели
]);
```

#### **Правильный вызов createClient:**
```php
$success = $xuiApiService->createClient($server, $inbound, $user, $clientEmail, $user->comment, $isDemo, $user->expiry_time);
```

### **2. XuiApiService**
**Файл:** `app/Services/XuiApiService.php`

#### **Правильная сигнатура метода:**
```php
public function createClient(XuiServer $server, XuiInbound $inbound, $user, string $clientEmail, string $comment, bool $isDemo = false, ?\Carbon\Carbon $expiryTime = null): bool
```

#### **Правильный расчет expirationTimestamp:**
```php
// Calculate expiration timestamp from User model
$expirationTimestamp = 0; // 0 means never expires
if ($user->expiry_time) {
    $expirationTimestamp = $user->expiry_time->timestamp * 1000; // X-UI expects milliseconds
}
```

#### **Правильные clientData:**
```php
$clientData = [
    'id' => $user->uuid,
    'flow' => 'xtls-rprx-vision',
    'email' => $clientEmail,
    'limitIp' => 0,
    'totalGB' => $user->total_gb, // ✅ Из User модели
    'expiryTime' => $expirationTimestamp, // ✅ Рассчитано из User
    'enable' => !$user->expired && !$user->disabled_at, // ✅ Из User модели
    'tgId' => $user->tg_id, // ✅ Из User модели
    'subId' => "unlimited_{$user->tg_id}", // ✅ Из User модели
    'comment' => $user->comment, // ✅ Из User модели
    'reset' => 0
];
```

## Тестовые сценарии

### **1. Expiration параметр (приоритет 1):**
```bash
curl -X POST "/api/user/create" \
     -d '{"tg_id": "100001", "demo": true, "expiration": 1735689600}'
```
**Результат:** expiry_time = 2024-01-01 00:00:00 (игнорирует demo флаг)

### **2. Demo флаг (приоритет 2):**
```bash
curl -X POST "/api/user/create" \
     -d '{"tg_id": "100002", "demo": true}'
```
**Результат:** expiry_time = now() + 1 день

### **3. По умолчанию (приоритет 3):**
```bash
curl -X POST "/api/user/create" \
     -d '{"tg_id": "100003"}'
```
**Результат:** expiry_time = now() + 1 неделя

### **4. Expiration в миллисекундах:**
```bash
curl -X POST "/api/user/create" \
     -d '{"tg_id": "100004", "expiration": 1735689600000}'
```
**Результат:** expiry_time = 2024-01-01 00:00:00

## Проверка полей

### **Все поля XuiClient должны браться из User:**
- ✅ `tg_id` → `$user->tg_id`
- ✅ `comment` → `$user->comment`
- ✅ `expiry_time` → `$user->expiry_time`
- ✅ `expired` → `$user->expired`
- ✅ `disabled_at` → `$user->disabled_at`
- ✅ `total_gb` → `$user->total_gb`
- ✅ `used_gb` → `$user->used_gb`
- ✅ `up_traffic` → `$user->up_traffic`
- ✅ `down_traffic` → `$user->down_traffic`
- ✅ `enable` → `!$user->expired && !$user->disabled_at`

### **Все поля X-UI API должны браться из User:**
- ✅ `tgId` → `$user->tg_id`
- ✅ `totalGB` → `$user->total_gb`
- ✅ `expiryTime` → `$user->expiry_time->timestamp * 1000`
- ✅ `enable` → `!$user->expired && !$user->disabled_at`
- ✅ `comment` → `$user->comment`

## Запуск тестов

```bash
php artisan test tests/Feature/UserFieldsFromUserModelTest.php
```

Все исправления обеспечивают правильное использование данных из User модели во всех компонентах системы!
