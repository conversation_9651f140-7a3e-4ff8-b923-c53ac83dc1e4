# Страница поддержки с системой рейтингов

## Обзор функциональности

### **Создана полноценная страница поддержки в стиле subscription-modern-simple.blade.php с:**
- ✅ Система оценки сервиса (5 эмоджи)
- ✅ Ограничение одна оценка в день
- ✅ Toast уведомления
- ✅ Кнопки перехода в Telegram и WhatsApp
- ✅ Отображение статуса подписки
- ✅ Адаптивный дизайн

## Структура файлов

### **1. Миграция базы данных**
**Файл:** `database/migrations/2024_12_30_000005_create_user_ratings_table.php`

```sql
CREATE TABLE user_ratings (
    id BIGINT PRIMARY KEY,
    user_id BIGINT FOREIGN KEY,
    rating TINYINT (1-5),
    user_ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### **2. Модель UserRating**
**Файл:** `app/Models/UserRating.php`

#### **Основные методы:**
- `canUserRateToday(int $userId)` - проверка возможности оценки
- `getUserLastRating(int $userId)` - последняя оценка пользователя
- `getAverageRating()` - средняя оценка сервиса
- `getRatingStats()` - статистика всех оценок

#### **Атрибуты:**
- `emoji` - эмоджи по рейтингу (😡😕😐🙂😄)
- `label` - текстовое описание рейтинга

### **3. Контроллер SupportController**
**Файл:** `app/Http/Controllers/SupportController.php`

#### **Методы:**
- `index(string $uuid)` - отображение страницы поддержки
- `submitRating(Request $request)` - отправка оценки
- `getRatingStats()` - статистика оценок (для админа)

### **4. Шаблон поддержки**
**Файл:** `resources/views/support/index.blade.php`

#### **Особенности:**
- Стиль как в subscription-modern-simple.blade.php
- Адаптивный дизайн с Tailwind CSS
- JavaScript для интерактивности
- Toast уведомления

## Система рейтингов

### **Логика оценок:**
```
1 = 😡 Очень плохо
2 = 😕 Плохо  
3 = 😐 Нормально
4 = 🙂 Хорошо
5 = 😄 Отлично
```

### **Ограничения:**
- ✅ Одна оценка в день на пользователя
- ✅ Валидация рейтинга (1-5)
- ✅ Сохранение IP и User-Agent
- ✅ Логирование всех оценок

### **Пример использования:**
```javascript
// Отправка оценки
fetch('/support/rating', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': token
    },
    body: JSON.stringify({
        uuid: userUuid,
        rating: 5
    })
});
```

## Маршруты

### **Веб-маршруты:**
```php
// Страница поддержки
GET /support/{uuid} → SupportController@index

// Отправка оценки
POST /support/rating → SupportController@submitRating

// Статистика оценок
GET /support/stats → SupportController@getRatingStats
```

### **Примеры URL:**
```
https://domain.com/support/550e8400-e29b-41d4-a716-************
```

## Конфигурация

### **Переменные окружения (.env):**
```env
TELEGRAM_SUPPORT=https://t.me/support_bot
WHATSAPP_SUPPORT=https://wa.me/1234567890
TELEGRAM_CHANNEL=https://t.me/smartvpn_channel
```

### **Конфигурация (config/app.php):**
```php
'telegram_support' => env('TELEGRAM_SUPPORT', 'https://t.me/support_bot'),
'whatsapp_support' => env('WHATSAPP_SUPPORT', 'https://wa.me/1234567890'),
'telegram_channel' => env('TELEGRAM_CHANNEL', 'https://t.me/smartvpn_channel'),
```

## Интеграция с подпиской

### **Кнопка поддержки добавлена в:**
- `resources/views/subs/subscription-modern-simple.blade.php`
- В разделе "Дополнительно"
- Ссылка: `{{ route('support.index', $data['uuid']) }}`

### **Навигация:**
```
Подписка → Дополнительно → Поддержка → Страница поддержки
Поддержка → Вернуться к подписке → Страница подписки
```

## Функциональность страницы

### **1. Информация о пользователе:**
- ID клиента (tg_id или uuid)
- Логотип SmartVPN
- Статус подписки с цветовой индикацией

### **2. Система оценки:**
- 5 эмоджи для оценки (1-5 звезд)
- Анимация при наведении и выборе
- Блокировка после оценки
- Уведомление об ограничении

### **3. Кнопки поддержки:**
- **Telegram поддержка** - синяя кнопка
- **WhatsApp поддержка** - зеленая кнопка  
- **Telegram канал** - фиолетовая кнопка
- **Вернуться к подписке** - серая кнопка

### **4. Toast уведомления:**
- Успешная отправка оценки
- Ошибки валидации
- Ограничения по времени
- Автоматическое скрытие через 5 секунд

## Статистика и аналитика

### **Сбор данных:**
```php
UserRating::create([
    'user_id' => $user->id,
    'rating' => $rating,
    'user_ip' => $request->ip(),
    'user_agent' => $request->userAgent(),
]);
```

### **Статистика оценок:**
```php
$stats = UserRating::getRatingStats();
// Возвращает:
[
    'total' => 150,
    'average' => 4.2,
    'distribution' => [1 => 5, 2 => 10, 3 => 25, 4 => 60, 5 => 50],
    'percentages' => [1 => 3.3, 2 => 6.7, 3 => 16.7, 4 => 40.0, 5 => 33.3]
]
```

### **Логирование:**
```php
Log::info('User rating submitted', [
    'user_id' => $user->id,
    'uuid' => $user->uuid,
    'tg_id' => $user->tg_id,
    'rating' => $rating,
    'emoji' => $emoji,
    'label' => $label,
    'ip' => $request->ip(),
]);
```

## Адаптивность и UX

### **Мобильная версия:**
- ✅ Адаптивные размеры кнопок
- ✅ Оптимизированные отступы
- ✅ Toast на всю ширину экрана
- ✅ Удобные touch-цели

### **Анимации:**
- ✅ Hover эффекты на кнопках
- ✅ Масштабирование эмоджи
- ✅ Плавные переходы
- ✅ Loading состояния

### **Доступность:**
- ✅ ARIA labels для эмоджи
- ✅ Keyboard navigation
- ✅ Screen reader support
- ✅ Семантическая разметка

## Безопасность

### **Защита от злоупотреблений:**
- ✅ CSRF токены
- ✅ Валидация UUID
- ✅ Ограничение по времени (1 раз в день)
- ✅ Логирование IP адресов

### **Валидация данных:**
```php
$request->validate([
    'uuid' => 'required|string',
    'rating' => 'required|integer|min:1|max:5',
]);
```

## Тестирование

### **Ручное тестирование:**
```bash
# Открыть страницу поддержки
curl "http://localhost:8000/support/550e8400-e29b-41d4-a716-************"

# Отправить оценку
curl -X POST "http://localhost:8000/support/rating" \
  -H "Content-Type: application/json" \
  -d '{"uuid":"550e8400-e29b-41d4-a716-************","rating":5}'

# Получить статистику
curl "http://localhost:8000/support/stats"
```

### **Проверка функций:**
- ✅ Отображение страницы поддержки
- ✅ Отправка оценки (успех)
- ✅ Повторная оценка (ошибка)
- ✅ Переходы по ссылкам
- ✅ Toast уведомления

Страница поддержки полностью готова и интегрирована в систему!
