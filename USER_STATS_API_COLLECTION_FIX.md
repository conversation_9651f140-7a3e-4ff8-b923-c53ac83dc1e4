# Исправление ошибки с методами коллекции в getUserStats API

## Проблема

### **Ошибка:**
```
Method Illuminate\Database\Eloquent\Collection::orWhereNotNull does not exist
```

### **Причина:**
В методе `getUserStats` мы пытались использовать методы Eloquent Builder (`whereNull`, `orWhereNotNull`) на коллекции Eloquent, что недопустимо. Эти методы работают только с Query Builder, а не с уже загруженными коллекциями.

### **Проблемный код:**
```php
// ❌ Неправильно - методы Builder на коллекции
$activeClients = $clients->where('enable', true)->whereNull('disabled_at')->where('expired', false)->count();
$disabledClients = $clients->where('enable', false)->orWhereNotNull('disabled_at')->count();
$expiredClients = $clients->where('expired', true)->count();
```

## Решение

### **Исправленный код:**
```php
// ✅ Правильно - методы коллекции с filter()
$activeClients = $clients->filter(function ($client) {
    return $client->enable && !$client->disabled_at && !$client->expired;
})->count();

$disabledClients = $clients->filter(function ($client) {
    return !$client->enable || $client->disabled_at;
})->count();

$expiredClients = $clients->filter(function ($client) {
    return $client->expired;
})->count();
```

### **Также исправлена логика can_connect:**
```php
// Calculate can_connect after we have activeClients count
$canConnect = !$isExpired && !$user->disabled_at && $activeClients > 0;

$statsData = [
    // ...
    'enable' => !$isExpired && !$user->disabled_at,
    'can_connect' => $canConnect,
    // ...
];
```

## Различия между методами

### **Query Builder методы (для запросов к БД):**
```php
// Работает с Query Builder
User::where('active', true)->whereNull('deleted_at')->get();
$user->xuiClients()->where('enable', true)->whereNull('disabled_at')->count();
```

### **Collection методы (для коллекций в памяти):**
```php
// Работает с коллекциями
$users->filter(function ($user) {
    return $user->active && !$user->deleted_at;
});

$clients->where('enable', true); // Простое сравнение
$clients->filter(function ($client) {
    return $client->enable && !$client->disabled_at; // Сложная логика
});
```

## Логика фильтрации клиентов

### **1. Активные клиенты:**
```php
$activeClients = $clients->filter(function ($client) {
    return $client->enable && !$client->disabled_at && !$client->expired;
})->count();
```
**Условия:** включен И не отключен И не истек

### **2. Отключенные клиенты:**
```php
$disabledClients = $clients->filter(function ($client) {
    return !$client->enable || $client->disabled_at;
})->count();
```
**Условия:** выключен ИЛИ отключен

### **3. Истекшие клиенты:**
```php
$expiredClients = $clients->filter(function ($client) {
    return $client->expired;
})->count();
```
**Условия:** истек

### **4. Возможность подключения:**
```php
$canConnect = !$isExpired && !$user->disabled_at && $activeClients > 0;
```
**Условия:** пользователь не истек И не отключен И есть активные клиенты

## Полный исправленный метод

### **Основные изменения:**
1. **Заменены методы Builder на методы коллекции**
2. **Исправлена логика расчета статистики клиентов**
3. **Добавлен правильный расчет can_connect**

### **Пример использования после исправления:**
```bash
curl "http://localhost:8000/api/user/stats?tg_id=100001"
```

### **Ожидаемый ответ:**
```json
{
    "success": true,
    "data": {
        "tg_id": "100001",
        "uuid": "550e8400-e29b-41d4-a716-************",
        "servers": {
            "Nederlands": [3, 4],
            "Moldova": [1]
        },
        "enable": true,
        "can_connect": true,
        "up": 4191841897,
        "down": 11131353542,
        "total": 1073741824,
        "total_clients": 3,
        "active_clients": 3,
        "disabled_clients": 0,
        "expired_clients": 0,
        "status": "active",
        "started_using": true
    }
}
```

## Тестирование

### **1. Тест активного пользователя:**
```bash
curl "http://localhost:8000/api/user/stats?tg_id=100001" | jq '.data | {enable, can_connect, active_clients}'
```

### **2. Тест отключенного пользователя:**
```bash
curl "http://localhost:8000/api/user/stats?tg_id=disabled_user" | jq '.data | {enable, can_connect, status}'
```

### **3. Тест истекшего пользователя:**
```bash
curl "http://localhost:8000/api/user/stats?tg_id=expired_user" | jq '.data | {enable, can_connect, is_expired}'
```

## Лучшие практики

### **1. Для запросов к БД используйте Query Builder:**
```php
// ✅ Правильно
$activeClients = $user->xuiClients()
    ->where('enable', true)
    ->whereNull('disabled_at')
    ->where('expired', false)
    ->count();
```

### **2. Для коллекций в памяти используйте Collection методы:**
```php
// ✅ Правильно
$activeClients = $clients->filter(function ($client) {
    return $client->enable && !$client->disabled_at && !$client->expired;
})->count();
```

### **3. Простые фильтры коллекций:**
```php
// ✅ Для простых условий
$enabledClients = $clients->where('enable', true);

// ✅ Для сложных условий
$complexFilter = $clients->filter(function ($client) {
    return $client->enable && !$client->disabled_at && $client->some_complex_logic;
});
```

## Производительность

### **Если нужна производительность, используйте запросы:**
```php
// Более эффективно для больших данных
$stats = $user->xuiClients()
    ->selectRaw('
        COUNT(*) as total_clients,
        SUM(CASE WHEN enable = 1 AND disabled_at IS NULL AND expired = 0 THEN 1 ELSE 0 END) as active_clients,
        SUM(CASE WHEN enable = 0 OR disabled_at IS NOT NULL THEN 1 ELSE 0 END) as disabled_clients,
        SUM(CASE WHEN expired = 1 THEN 1 ELSE 0 END) as expired_clients
    ')
    ->first();
```

### **Для небольших коллекций коллекции удобнее:**
```php
// Проще для понимания и отладки
$activeClients = $clients->filter(fn($c) => $c->enable && !$c->disabled_at && !$c->expired)->count();
```

Ошибка исправлена, API работает корректно!
