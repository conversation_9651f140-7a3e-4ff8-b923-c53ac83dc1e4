# API для продления подписки пользователей

## Обзор функциональности

### **Создан API endpoint для продления подписки:**
- ✅ Метод `POST /api/user/extend-subscription`
- ✅ Поддержка поиска пользователя по `tg_id`, `uuid` или `email`
- ✅ Валидация входных данных
- ✅ Обновление срока на XUI серверах
- ✅ История продлений в БД

## Структура базы данных

### **Таблица subscription_extensions**
```sql
CREATE TABLE subscription_extensions (
    id BIGINT PRIMARY KEY,
    user_id BIGINT FOREIGN KEY REFERENCES users(id),
    old_expiry_time TIMESTAMP NULL,
    new_expiry_time TIMESTAMP NULL,
    source VARCHAR(255) NULL,
    comment TEXT NULL,
    request_ip VARCHAR(45) NULL,
    request_user_agent TEXT NULL,
    additional_data JSON NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX(user_id, created_at),
    INDEX(source),
    INDEX(created_at),
    INDEX(user_id, source)
);
```

### **Поля таблицы:**
- **user_id** - ID пользователя
- **old_expiry_time** - Предыдущий срок действия
- **new_expiry_time** - Новый срок действия
- **source** - Источник запроса (api, telegram_bot, admin, etc.)
- **comment** - Комментарий к продлению
- **request_ip** - IP адрес запроса
- **request_user_agent** - User Agent запроса
- **additional_data** - Дополнительные данные в JSON

## API Endpoint

### **URL:** `POST /api/user/extend-subscription`

### **Параметры запроса:**
```json
{
    "tg_id": "123456789",           // Опционально - Telegram ID
    "uuid": "user-uuid-here",       // Опционально - UUID пользователя
    "email": "<EMAIL>",    // Опционально - Email пользователя
    "expiration": 1735689600,       // Обязательно - Unix timestamp нового срока
    "comment": "Продление на месяц", // Опционально - Комментарий
    "source": "telegram_bot"        // Опционально - Источник запроса
}
```

### **Правила валидации:**
- **Один из идентификаторов обязателен:** `tg_id`, `uuid` или `email`
- **expiration:** обязательный, integer, больше 0, должен быть в будущем
- **comment:** опциональный, строка, максимум 1000 символов
- **source:** опциональный, строка, максимум 100 символов

### **Успешный ответ (200):**
```json
{
    "success": true,
    "message": "Subscription extended successfully",
    "data": {
        "user_id": 123,
        "uuid": "user-uuid-here",
        "tg_id": "123456789",
        "old_expiry_time": "2024-12-30T10:00:00.000000Z",
        "new_expiry_time": "2025-01-30T10:00:00.000000Z",
        "extension_days": 31,
        "extension_duration": "+31 день",
        "updated_servers": ["Server1", "Server2"],
        "failed_servers": [],
        "extension_id": 456
    }
}
```

### **Ошибки:**

#### **Пользователь не найден (404):**
```json
{
    "success": false,
    "message": "User not found"
}
```

#### **Валидация не пройдена (422):**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "expiration": ["The expiration field is required."],
        "tg_id": ["At least one of tg_id, uuid, or email is required"]
    }
}
```

#### **Срок в прошлом (422):**
```json
{
    "success": false,
    "message": "New expiry time must be in the future"
}
```

#### **Внутренняя ошибка (500):**
```json
{
    "success": false,
    "message": "Failed to extend subscription",
    "error": "Database connection failed"
}
```

## Модель SubscriptionExtension

### **Файл:** `app/Models/SubscriptionExtension.php`

### **Основные методы:**

#### **Получение продлений пользователя:**
```php
$extensions = SubscriptionExtension::getForUser($userId, 10);
```

#### **Получение продлений по источнику:**
```php
$extensions = SubscriptionExtension::getBySource('telegram_bot', 100);
```

#### **Статистика продлений:**
```php
$stats = SubscriptionExtension::getStats(
    Carbon::now()->subMonth(), // От
    Carbon::now()              // До
);

// Результат:
[
    'total' => 150,
    'successful' => 145,
    'failed' => 5,
    'by_source' => [
        'telegram_bot' => 80,
        'api' => 50,
        'admin' => 15
    ],
    'total_days_extended' => 3100,
    'average_extension_days' => 21.4
]
```

#### **Атрибуты модели:**
```php
$extension->extension_days;        // Количество дней продления
$extension->extension_duration;    // "+30 дней" или "-5 дней"
$extension->source_label;          // "Telegram Bot" вместо "telegram_bot"
$extension->isSuccessful();        // true если продление успешно
```

## Логика работы API

### **1. Поиск пользователя:**
```php
// Приоритет поиска: tg_id -> uuid -> email
if ($request->tg_id) {
    $user = User::where('tg_id', $request->tg_id)->first();
} elseif ($request->uuid) {
    $user = User::where('uuid', $request->uuid)->first();
} elseif ($request->email) {
    $user = User::where('email', $request->email)->first();
}
```

### **2. Обновление в транзакции:**
```php
DB::beginTransaction();
try {
    // 1. Обновить пользователя
    $user->update([
        'expiry_time' => $newExpiryTime,
        'expired' => false,
    ]);

    // 2. Создать запись истории
    $extension = SubscriptionExtension::create([...]);

    // 3. Обновить на XUI серверах
    foreach ($clients as $client) {
        $xuiApiService->updateClientExpiryTime(...);
    }

    DB::commit();
} catch (Exception $e) {
    DB::rollBack();
    throw $e;
}
```

### **3. Обновление на XUI серверах:**
```php
// Для каждого активного клиента пользователя
foreach ($user->xuiClients() as $client) {
    $server = $client->xuiServer;
    $inbound = $client->xuiInbound;
    
    if ($server->is_active) {
        $success = $xuiApiService->updateClientExpiryTime(
            $server,
            $inbound,
            $client->client_id,
            $newExpiryTime
        );
        
        if ($success) {
            $updatedServers[] = $server->name;
            $client->update(['expiry_time' => $newExpiryTime]);
        } else {
            $failedServers[] = $server->name;
        }
    }
}
```

## Метод updateClientExpiryTime в XuiApiService

### **Алгоритм работы:**
1. **Аутентификация** на XUI сервере
2. **Получение списка inbound'ов** через `/panel/inbound/list`
3. **Поиск нужного inbound** по ID
4. **Парсинг настроек** и поиск клиента по ID
5. **Обновление expiryTime** клиента
6. **Отправка обновленных настроек** через `/panel/inbound/update/{id}`

### **Код метода:**
```php
public function updateClientExpiryTime(XuiServer $server, XuiInbound $inbound, string $clientId, Carbon $newExpiryTime): bool
{
    // 1. Аутентификация
    if (!$this->authenticate($server)) {
        return false;
    }

    // 2. Получение inbound данных
    $response = $httpClient->post('/panel/inbound/list');
    $inboundData = // найти нужный inbound

    // 3. Обновление клиента
    $settings = json_decode($inboundData['settings'], true);
    foreach ($settings['clients'] as &$client) {
        if ($client['id'] === $clientId) {
            $client['expiryTime'] = $newExpiryTime->timestamp * 1000;
            break;
        }
    }

    // 4. Отправка обновлений
    $updateResponse = $httpClient->post('/panel/inbound/update/' . $inbound->inbound_id, [
        'id' => $inbound->inbound_id,
        'settings' => json_encode($settings),
        'streamSettings' => $inboundData['streamSettings'],
        'sniffing' => $inboundData['sniffing'],
    ]);

    return $updateResponse->successful();
}
```

## Примеры использования

### **1. Продление через Telegram Bot:**
```bash
curl -X POST http://your-domain.com/api/user/extend-subscription \
  -H "Content-Type: application/json" \
  -d '{
    "tg_id": "123456789",
    "expiration": 1735689600,
    "comment": "Продление через бота на 30 дней",
    "source": "telegram_bot"
  }'
```

### **2. Продление через админку:**
```bash
curl -X POST http://your-domain.com/api/user/extend-subscription \
  -H "Content-Type: application/json" \
  -d '{
    "uuid": "550e8400-e29b-41d4-a716-************",
    "expiration": 1738281600,
    "comment": "Ручное продление администратором",
    "source": "admin"
  }'
```

### **3. Автоматическое продление после оплаты:**
```bash
curl -X POST http://your-domain.com/api/user/extend-subscription \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "expiration": 1740960000,
    "comment": "Автоматическое продление после оплаты",
    "source": "payment"
  }'
```

## Логирование

### **Успешное продление:**
```php
Log::info('Subscription extended successfully', [
    'user_id' => $user->id,
    'uuid' => $user->uuid,
    'tg_id' => $user->tg_id,
    'old_expiry' => $oldExpiryTime?->toISOString(),
    'new_expiry' => $newExpiryTime->toISOString(),
    'source' => $request->source ?? 'api',
    'comment' => $request->comment,
    'updated_servers' => $updatedServers,
    'failed_servers' => $failedServers,
    'extension_id' => $extension->id,
]);
```

### **Ошибки:**
```php
Log::error('Failed to extend subscription', [
    'request_data' => $request->all(),
    'error' => $e->getMessage(),
    'trace' => $e->getTraceAsString(),
]);
```

## Связи в моделях

### **User модель:**
```php
public function subscriptionExtensions(): HasMany
{
    return $this->hasMany(SubscriptionExtension::class);
}

// Использование:
$user = User::find(1);
$extensions = $user->subscriptionExtensions()->latest()->take(10)->get();
```

### **SubscriptionExtension модель:**
```php
public function user(): BelongsTo
{
    return $this->belongsTo(User::class);
}

// Использование:
$extension = SubscriptionExtension::find(1);
$user = $extension->user;
```

## Безопасность

### **Валидация:**
- ✅ Проверка существования пользователя
- ✅ Валидация Unix timestamp
- ✅ Проверка что новый срок в будущем
- ✅ Ограничение длины комментария и источника

### **Транзакции:**
- ✅ Все операции в БД транзакции
- ✅ Откат при ошибках
- ✅ Консистентность данных

### **Логирование:**
- ✅ Все операции логируются
- ✅ IP адрес и User Agent сохраняются
- ✅ Детальная информация об ошибках

API для продления подписки полностью реализован и готов к использованию!
