<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'description',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get setting value by key.
     */
    public static function get(string $key, $default = null, $noCache = false)
    {
        if ($noCache) {
            $setting = self::where('key', $key)->first();

            return $setting ? $setting->value : $default;
        }

        return Cache::remember("setting.{$key}", 1800, function () use ($key, $default) {
            $setting = self::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Set setting value by key.
     */
    public static function set(string $key, $value, ?string $description = null): self
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'description' => $description,
            ]
        );

        // Clear cache
        Cache::forget("setting.{$key}");

        return $setting;
    }

    /**
     * Check if setting exists.
     */
    public static function has(string $key): bool
    {
        return self::where('key', $key)->exists();
    }

    /**
     * Delete setting by key.
     */
    public static function remove(string $key): bool
    {
        $deleted = self::where('key', $key)->delete();

        if ($deleted) {
            Cache::forget("setting.{$key}");
        }

        return $deleted > 0;
    }

    /**
     * Get all settings as key-value array.
     */
    public static function getAll(): array
    {
        return self::pluck('value', 'key')->toArray();
    }

    /**
     * Clear all settings cache.
     */
    public static function clearCache(): void
    {
        $keys = self::pluck('key');
        foreach ($keys as $key) {
            Cache::forget("setting.{$key}");
        }
    }

    /**
     * Boot method to clear cache on model events.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($setting) {
            Cache::forget("setting.{$setting->key}");
        });

        static::deleted(function ($setting) {
            Cache::forget("setting.{$setting->key}");
        });
    }
}
