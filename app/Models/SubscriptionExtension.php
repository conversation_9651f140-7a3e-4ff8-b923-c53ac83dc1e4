<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubscriptionExtension extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'old_expiry_time',
        'new_expiry_time',
        'source',
        'comment',
        'request_ip',
        'request_user_agent',
        'additional_data',
    ];

    protected $casts = [
        'old_expiry_time' => 'datetime',
        'new_expiry_time' => 'datetime',
        'additional_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the extension.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get extension duration in days.
     */
    public function getExtensionDaysAttribute(): ?int
    {
        if (!$this->old_expiry_time || !$this->new_expiry_time) {
            return null;
        }

        return $this->new_expiry_time->diffInDays($this->old_expiry_time, false);
    }

    /**
     * Get extension duration in human readable format.
     */
    public function getExtensionDurationAttribute(): string
    {
        $days = $this->extension_days;
        
        if ($days === null) {
            return 'Неизвестно';
        }

        if ($days === 0) {
            return 'Без изменений';
        }

        if ($days > 0) {
            return "+{$days} " . $this->pluralizeDays($days);
        }

        return "{$days} " . $this->pluralizeDays(abs($days));
    }

    /**
     * Get source label.
     */
    public function getSourceLabelAttribute(): string
    {
        return match($this->source) {
            'telegram_bot' => 'Telegram Bot',
            'api' => 'API',
            'admin' => 'Администратор',
            'payment' => 'Оплата',
            'manual' => 'Ручное продление',
            'auto' => 'Автоматическое продление',
            default => $this->source ?? 'Неизвестно',
        };
    }

    /**
     * Check if extension was successful.
     */
    public function isSuccessful(): bool
    {
        return $this->new_expiry_time !== null;
    }

    /**
     * Get extensions for user.
     */
    public static function getForUser(int $userId, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get extensions by source.
     */
    public static function getBySource(string $source, int $limit = 100): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('source', $source)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get extension statistics.
     */
    public static function getStats(?\Carbon\Carbon $from = null, ?\Carbon\Carbon $to = null): array
    {
        $query = self::query();

        if ($from) {
            $query->where('created_at', '>=', $from);
        }

        if ($to) {
            $query->where('created_at', '<=', $to);
        }

        $extensions = $query->get();

        $stats = [
            'total' => $extensions->count(),
            'successful' => $extensions->where('new_expiry_time', '!=', null)->count(),
            'failed' => $extensions->where('new_expiry_time', null)->count(),
            'by_source' => [],
            'total_days_extended' => 0,
            'average_extension_days' => 0,
        ];

        // Group by source
        $bySource = $extensions->groupBy('source');
        foreach ($bySource as $source => $items) {
            $stats['by_source'][$source] = $items->count();
        }

        // Calculate total days extended
        $totalDays = 0;
        $validExtensions = 0;
        
        foreach ($extensions as $extension) {
            if ($extension->extension_days !== null && $extension->extension_days > 0) {
                $totalDays += $extension->extension_days;
                $validExtensions++;
            }
        }

        $stats['total_days_extended'] = $totalDays;
        $stats['average_extension_days'] = $validExtensions > 0 ? round($totalDays / $validExtensions, 1) : 0;

        return $stats;
    }

    /**
     * Pluralize days in Russian.
     */
    private function pluralizeDays(int $days): string
    {
        $days = abs($days);
        
        if ($days % 10 === 1 && $days % 100 !== 11) {
            return 'день';
        }
        
        if (in_array($days % 10, [2, 3, 4]) && !in_array($days % 100, [12, 13, 14])) {
            return 'дня';
        }
        
        return 'дней';
    }
}
