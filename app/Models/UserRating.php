<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserRating extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'rating',
        'user_ip',
        'user_agent',
    ];

    protected $casts = [
        'rating' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the rating.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get rating emoji based on rating value.
     */
    public function getEmojiAttribute(): string
    {
        return match($this->rating) {
            1 => '😡',
            2 => '😕',
            3 => '😐',
            4 => '🙂',
            5 => '😄',
            default => '😐',
        };
    }

    /**
     * Get rating label based on rating value.
     */
    public function getLabelAttribute(): string
    {
        return match($this->rating) {
            1 => 'Очень плохо',
            2 => 'Плохо',
            3 => 'Нормально',
            4 => 'Хорошо',
            5 => 'Отлично',
            default => 'Нормально',
        };
    }

    /**
     * Check if user can rate today (once per day limit).
     */
    public static function canUserRateToday(int $userId): bool
    {
        $today = now()->startOfDay();
        
        return !self::where('user_id', $userId)
            ->where('created_at', '>=', $today)
            ->exists();
    }

    /**
     * Get user's last rating.
     */
    public static function getUserLastRating(int $userId): ?self
    {
        return self::where('user_id', $userId)
            ->latest()
            ->first();
    }

    /**
     * Get average rating for the service.
     */
    public static function getAverageRating(): float
    {
        return self::avg('rating') ?? 0;
    }

    /**
     * Get rating statistics.
     */
    public static function getRatingStats(): array
    {
        $stats = self::selectRaw('rating, COUNT(*) as count')
            ->groupBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        $total = array_sum($stats);
        $average = $total > 0 ? array_sum(array_map(fn($rating, $count) => $rating * $count, array_keys($stats), $stats)) / $total : 0;

        return [
            'total' => $total,
            'average' => round($average, 2),
            'distribution' => [
                1 => $stats[1] ?? 0,
                2 => $stats[2] ?? 0,
                3 => $stats[3] ?? 0,
                4 => $stats[4] ?? 0,
                5 => $stats[5] ?? 0,
            ],
            'percentages' => $total > 0 ? [
                1 => round((($stats[1] ?? 0) / $total) * 100, 1),
                2 => round((($stats[2] ?? 0) / $total) * 100, 1),
                3 => round((($stats[3] ?? 0) / $total) * 100, 1),
                4 => round((($stats[4] ?? 0) / $total) * 100, 1),
                5 => round((($stats[5] ?? 0) / $total) * 100, 1),
            ] : [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0],
        ];
    }
}
