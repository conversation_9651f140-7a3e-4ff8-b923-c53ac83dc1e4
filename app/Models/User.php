<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'uuid',
        'name',
        'email',
        'tg_id',
        'demo_until',
        'comment',
        'expiry_time',
        'expired',
        'disabled_at',
        'total_gb',
        'used_gb',
        'up_traffic',
        'down_traffic',
        'use_common_routing',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'demo_until' => 'datetime',
            'expiry_time' => 'datetime',
            'expired' => 'boolean',
            'disabled_at' => 'datetime',
            'total_gb' => 'integer',
            'used_gb' => 'integer',
            'up_traffic' => 'integer',
            'down_traffic' => 'integer',
            'use_common_routing' => 'boolean',
            'password' => 'hashed',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->uuid)) {
                $user->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the XUI clients for the user.
     */
    public function xuiClients(): HasMany
    {
        return $this->hasMany(XuiClient::class);
    }

    /**
     * Get the ratings for the user.
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(UserRating::class);
    }

    /**
     * Find user by UUID.
     */
    public static function findByUuid(string $uuid): ?self
    {
        return static::where('uuid', $uuid)->first();
    }

    /**
     * Find or create user by tg_id or email.
     */
    public static function findOrCreateByClientIdOrTgIdOrEmail(?string $clientId,  ?string $tgId, ?string $email): ?self
    {
        if (!$clientId && !$tgId && !$email) {
            return null;
        }

        $query = static::query();

        if ($clientId) {
            $query->where('uuid', $clientId);
        } elseif ($tgId) {
            $query->where('tg_id', $tgId);
        } elseif ($email) {
            $query->where('email', $email);
        }

        if (preg_match('/client(\d+)-?/', $email, $matches)) {
            $tgId = $matches[1];
            $query->where('tg_id', $tgId)
                    ->orWhere('email', "client{$tgId}");
        }

        $user = $query->first();

        if (!$user && ($tgId || $email)) {
            $user = static::create([
                'uuid' => Str::uuid(),
                'tg_id' => $tgId,
                'email' => $email,
                'name' => $email ?? "User_{$tgId}",
            ]);
        }

        return $user;
    }
}
