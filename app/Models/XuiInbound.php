<?php

namespace App\Models;

use App\Services\ServerStatusService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class XuiInbound extends Model
{
    use HasFactory;

    protected $fillable = [
        'xui_server_id',
        'inbound_id',
        'up',
        'down',
        'total',
        'remark',
        'enable',
        'expiry_time',
        'listen',
        'port',
        'protocol',
        'settings',
        'tag',
        'sniffing',
        'allocate',
        'stream_settings',
        'raw_data',
    ];

    protected function casts(): array
    {
        return [
            'enable' => 'boolean',
            'expiry_time' => 'integer',
            'up' => 'integer',
            'down' => 'integer',
            'total' => 'integer',
            'port' => 'integer',
            'settings' => 'array',
            'sniffing' => 'array',
            'allocate' => 'array',
            'stream_settings' => 'array',
            'raw_data' => 'array',
        ];
    }

    /**
     * Get the XUI server that owns this inbound.
     */
    public function xuiServer(): BelongsTo
    {
        return $this->belongsTo(XuiServer::class);
    }

    /**
     * Get the clients for this inbound.
     */
    public function xuiClients(): HasMany
    {
        return $this->hasMany(XuiClient::class);
    }

    /**
     * Check if inbound is active.
     */
    public function isActive(): bool
    {
        if (!$this->enable) {
            return false;
        }

        if ($this->expiry_time && $this->expiry_time < time()) {
            return false;
        }

        return true;
    }

    /**
     * Get clients from settings JSON.
     */
    public function getClientsFromSettings(): array
    {
        if (!$this->settings || !isset($this->settings['clients'])) {
            return [];
        }

        return $this->settings['clients'];
    }

    /**
     * Generate vless URL for a specific client.
     */
    public function generateVlessUrl(array $clientData): string
    {
        $host = $this->xuiServer->host;
        $port = $this->port;
        $clientId = $clientData['id'];
        $flow = $clientData['flow'] ?? '';
        $remark = $this->remark;
        $clientEmail = $clientData['email'] ?? '';

        // Build the remark with client info and server load
        // $urlRemark = $remark . '-' . $clientEmail; // временно закомменируем, чтобы потестировать без email
        $urlRemark = $remark;

        // Add server load information if available
        if ($this->xuiServer->server_load !== null) {
            $loadPercentage = number_format($this->xuiServer->server_load, 2);
            // $urlRemark .= " ({$loadPercentage}%)";
            $urlRemark .= " " . app(ServerStatusService::class)->toBrailleLoadChar($this->xuiServer->server_load);
            // $urlRemark .= " " . app(ServerStatusService::class)->toSuperscriptNumber($this->xuiServer->server_load);
        }

        $urlRemark = str_replace(' ', ' ', $urlRemark);

        // Base vless URL
        $vlessUrl = "vless://{$clientId}@{$host}:{$port}";

        // Build query parameters from stream settings
        $params = [];

        if ($this->stream_settings) {
            $streamSettings = $this->stream_settings;

            // Network type
            if (isset($streamSettings['network'])) {
                $params['type'] = $streamSettings['network'];
            }

            // Security
            if (isset($streamSettings['security'])) {
                $params['security'] = $streamSettings['security'];
            }

            // Reality settings
            if (isset($streamSettings['realitySettings']['settings'])) {
                $realitySettings = $streamSettings['realitySettings']['settings'];

                if (isset($realitySettings['publicKey'])) {
                    $params['pbk'] = $realitySettings['publicKey'];
                }

                if (isset($realitySettings['fingerprint'])) {
                    $params['fp'] = $realitySettings['fingerprint'];
                }

                if (isset($realitySettings['spiderX'])) {
                    $params['spx'] = $realitySettings['spiderX'];
                }
            }

            // Server names (use first one as SNI)
            if (isset($streamSettings['realitySettings']['serverNames'][0])) {
                $params['sni'] = $streamSettings['realitySettings']['serverNames'][0];
            }

            // Short IDs (use first one as SID)
            if (isset($streamSettings['realitySettings']['shortIds'][0])) {
                $params['sid'] = $streamSettings['realitySettings']['shortIds'][0];
            }
        }

        // Add flow if present
        if ($flow) {
            $params['flow'] = $flow;
        }

        // Build query string
        $queryString = http_build_query($params);

        // Combine URL parts
        $fullUrl = $vlessUrl . '?' . $queryString . '#' . urlencode($urlRemark);

        return $fullUrl;
    }
}
