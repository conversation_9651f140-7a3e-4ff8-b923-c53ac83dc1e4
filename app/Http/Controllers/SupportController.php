<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserRating;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class SupportController extends Controller
{
    /**
     * Show support page for user by UUID.
     */
    public function index(string $uuid)
    {
        try {
            // Find user by UUID
            $user = User::where('uuid', $uuid)->first();

            if (!$user) {
                abort(404, 'Пользователь не найден');
            }

            // Get user's subscription info
            $clients = $user->xuiClients()
                ->with(['xuiServer', 'xuiInbound'])
                ->whereHas('xuiServer', function ($query) {
                    $query->where('is_active', true);
                })
                ->get();

            // Calculate subscription status
            $now = now();
            $isExpired = $user->expiry_time && $user->expiry_time->isPast();
            $isDemo = !is_null($user->demo_until);
            $daysUntilExpiry = null;
            
            if ($user->expiry_time) {
                $daysUntilExpiry = $user->expiry_time->diffInDays($now, false);
                if ($daysUntilExpiry < 0) {
                    $daysUntilExpiry = 0;
                }
            }

            // Determine status
            $status = 'Активна';
            $statusColor = 'text-green-400';
            
            if ($user->disabled_at) {
                $status = 'Отключена';
                $statusColor = 'text-red-400';
            } elseif ($isExpired) {
                $status = 'Истекла';
                $statusColor = 'text-red-400';
            } elseif ($isDemo) {
                $status = 'Демо';
                $statusColor = 'text-yellow-400';
            }

            // Check if user can rate today
            $canRate = UserRating::canUserRateToday($user->id);
            $lastRating = UserRating::getUserLastRating($user->id);

            // Support contacts from config
            $telegramSupport = config('app.telegram_support', 'https://t.me/support_bot');
            $whatsappSupport = config('app.whatsapp_support', 'https://wa.me/1234567890');
            $telegramChannel = config('app.telegram_channel', 'https://t.me/smartvpn_channel');

            return view('support.index', compact(
                'user',
                'clients',
                'status',
                'statusColor',
                'isExpired',
                'isDemo',
                'daysUntilExpiry',
                'canRate',
                'lastRating',
                'telegramSupport',
                'whatsappSupport',
                'telegramChannel'
            ));

        } catch (\Exception $e) {
            Log::error('Support page error', [
                'uuid' => $uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            abort(500, 'Ошибка загрузки страницы поддержки');
        }
    }

    /**
     * Submit user rating.
     */
    public function submitRating(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'uuid' => 'required|string',
                'rating' => 'required|integer|min:1|max:5',
            ]);

            // Find user
            $user = User::where('uuid', $request->uuid)->first();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Пользователь не найден',
                ], 404);
            }

            // Check if user can rate today
            if (!UserRating::canUserRateToday($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Вы уже оценили сервис сегодня. Попробуйте завтра!',
                ], 429);
            }

            // Create rating
            $rating = UserRating::create([
                'user_id' => $user->id,
                'rating' => $request->rating,
                'user_ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Get rating emoji and label
            $emoji = $rating->emoji;
            $label = $rating->label;

            Log::info('User rating submitted', [
                'user_id' => $user->id,
                'uuid' => $user->uuid,
                'tg_id' => $user->tg_id,
                'rating' => $request->rating,
                'emoji' => $emoji,
                'label' => $label,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => "Спасибо за оценку! {$emoji} {$label}",
                'data' => [
                    'rating' => $request->rating,
                    'emoji' => $emoji,
                    'label' => $label,
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Некорректные данные',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Rating submission error', [
                'uuid' => $request->uuid ?? 'unknown',
                'rating' => $request->rating ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при сохранении оценки',
            ], 500);
        }
    }

    /**
     * Get rating statistics (for admin).
     */
    public function getRatingStats(): JsonResponse
    {
        try {
            $stats = UserRating::getRatingStats();

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            Log::error('Rating stats error', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ошибка получения статистики',
            ], 500);
        }
    }
}
