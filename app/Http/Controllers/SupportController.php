<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserRating;
use App\Services\HelperService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class SupportController extends Controller
{
    public function __construct(
        private HelperService $helperService,
    ) {}

    /**
     * Show support page for user by UUID.
     */
    public function index(string $uuid)
    {
        try {
            // Find user by UUID
            $user = User::where('uuid', $uuid)->first();

            if (!$user) {
                abort(404, 'Пользователь не найден');
            }

            // Get user's subscription info
            $clients = $user->xuiClients()
                ->with(['xuiServer', 'xuiInbound'])
                ->whereHas('xuiServer', function ($query) {
                    $query->where('is_active', true);
                })
                ->get();

            // Calculate subscription status (same logic as SubscriptionController)
            $now = now();

            // Get statistics from User model (centralized source)
            $totalUpload = $user->up_traffic;
            $totalDownload = $user->down_traffic;
            $totalLimit = $user->total_gb;
            $earliestExpiry = $user->expiry_time;

            // Determine status
            $isExpired = $earliestExpiry && $earliestExpiry->isPast();
            $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
            $isDemo = !is_null($user->demo_until);

            $daysUntilExpiry = null;
            if ($earliestExpiry) {
                $daysUntilExpiry = $earliestExpiry->diffInDays($now, false);
                if ($daysUntilExpiry < 0) {
                    $daysUntilExpiry = 0;
                }
            }

            // Determine status (same logic as SubscriptionController)
            $status = 'активна';
            $statusColor = 'text-green-200';

            if ($user->disabled_at) {
                $status = 'отключена';
                $statusColor = 'text-red-200';
            } elseif ($isLimited) {
                $status = 'лимит исчерпан';
                $statusColor = 'text-yellow-200';
            } elseif ($isExpired) {
                $status = 'истекла';
                $statusColor = 'text-red-200';
            } elseif ($isDemo) {
                $status = 'демо';
                $statusColor = 'text-yellow-200';
            }

            // Get user's rating for today (if exists)
            $todayRating = UserRating::getUserTodayRating($user->id);
            $lastRating = UserRating::getUserLastRating($user->id);

            // Support contacts from config
            $telegramSupport = $this->helperService->getTelegramSupportLink($user);
            $telegramSupportDeepLink = $this->helperService->getTelegramSupportDeepLink($user);

            $whatsappSupport = $this->helperService->getWhatsappSupportLink($user);
            $whatsappSupportDeepLink = $this->helperService->getWhatsappSupportDeepLink($user);

            $telegramChannel = config('app.telegram_channel', '#');
            $referralLink = $this->helperService->getTelegramReferralLink($user);


            $data = [
                'client_id' => str_replace("client", "", $user->email),
                'email' => $user->email ?? 'User',
            ];

            return view('support.index', compact(
                'data',
                'user',
                'clients',
                'status',
                'statusColor',
                'isExpired',
                'isDemo',
                'isLimited',
                'daysUntilExpiry',
                'totalUpload',
                'totalDownload',
                'totalLimit',
                'earliestExpiry',
                'todayRating',
                'lastRating',
                'telegramSupport',
                'telegramSupportDeepLink',
                'whatsappSupport',
                'whatsappSupportDeepLink',
                'telegramChannel',
                'referralLink',
            ));

        } catch (\Exception $e) {
            Log::error('Support page error', [
                'uuid' => $uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            abort(500, 'Ошибка загрузки страницы поддержки');
        }
    }

    /**
     * Submit user rating.
     */
    public function submitRating(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'uuid' => 'required|string',
                'rating' => 'required|integer|min:1|max:5',
            ]);

            // Find user
            $user = User::where('uuid', $request->uuid)->first();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Пользователь не найден',
                ], 404);
            }

            // Create or update rating for today
            $rating = UserRating::createOrUpdateTodayRating(
                $user->id,
                $request->rating,
                $request->ip(),
                $request->userAgent()
            );

            // Get rating emoji and label
            $emoji = $rating->emoji;
            $label = $rating->label;
            $isUpdate = $rating->wasRecentlyCreated ? false : true;

            Log::info('User rating submitted', [
                'user_id' => $user->id,
                'uuid' => $user->uuid,
                'tg_id' => $user->tg_id,
                'rating' => $request->rating,
                'emoji' => $emoji,
                'label' => $label,
                'is_update' => $isUpdate,
                'ip' => $request->ip(),
            ]);

            $message = $isUpdate
                ? "Оценка обновлена! {$emoji} {$label}"
                : "Спасибо за оценку! {$emoji} {$label}";

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'rating' => $request->rating,
                    'emoji' => $emoji,
                    'label' => $label,
                    'is_update' => $isUpdate,
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Некорректные данные',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Rating submission error', [
                'uuid' => $request->uuid ?? 'unknown',
                'rating' => $request->rating ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при сохранении оценки',
            ], 500);
        }
    }

    /**
     * Get rating statistics (for admin).
     */
    public function getRatingStats(): JsonResponse
    {
        try {
            $stats = UserRating::getRatingStats();

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            Log::error('Rating stats error', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ошибка получения статистики',
            ], 500);
        }
    }
}
