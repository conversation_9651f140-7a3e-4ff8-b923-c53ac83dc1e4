<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\XuiServer;
use App\Models\XuiInbound;
use App\Models\XuiClient;
use App\Services\XuiApiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SubscriptionApiController extends Controller
{
    /**
     * Get subscription link by user identifier.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSubscriptionLink(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'uuid' => 'nullable|uuid',
                'tg_id' => 'nullable|string',
                'email' => 'nullable|email',
                'client_id' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Check that at least one identifier is provided
            $uuid = $request->input('uuid');
            $tgId = $request->input('tg_id');
            $email = $request->input('email');
            $clientId = $request->input('client_id');

            if (!$tgId && !$email && !$clientId) {
                return response()->json([
                    'success' => false,
                    'message' => 'At least one identifier (uuid, tg_id, email, or client_id) must be provided'
                ], 400);
            }

            // Find user by provided identifier
            $user = $this->findUserByIdentifier($uuid, $tgId, $email, $clientId);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            // Generate subscription link
            $subscriptionLink = $this->generateSubscriptionLink($user->uuid);

            Log::info('Subscription link requested', [
                'user_id' => $user->id,
                'uuid' => $user->uuid,
                'tg_id' => $tgId,
                'email' => $email,
                'client_id' => $clientId,
                'link' => $subscriptionLink
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription_link' => $subscriptionLink,
                    'uuid' => $user->uuid,
                    'user' => [
                        'id' => $user->id,
                        'email' => $user->email,
                        'tg_id' => $user->tg_id,
                        'created_at' => $user->created_at,
                        'updated_at' => $user->updated_at,
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting subscription link', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Find user by one of the provided identifiers.
     *
     * @param string|null $tgId
     * @param string|null $email
     * @param string|null $clientId
     * @return User|null
     */
    private function findUserByIdentifier(?string $uuid, ?string $tgId, ?string $email, ?string $clientId): ?User
    {
        // dd($uuid, $tgId, $email, $clientId);
        // Try to find by uuid
        if ($uuid) {
            $user = User::where('uuid', $uuid)->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by tg_id first
        if ($tgId) {
            $user = User::where('tg_id', $tgId)->first();
            if ($user) {
                return $user;
            }
            $user = User::where('email', "client{$tgId}")->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by email
        if ($email) {
            $user = User::where('email', $email)->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by client_id (assuming it's stored in email field with "client" prefix)
        if ($clientId) {
            $emailFromClientId = "client{$clientId}";
            $user = User::where('email', $emailFromClientId)->first();
            if ($user) {
                return $user;
            }
        }

        return null;
    }

    /**
     * Generate subscription link for user UUID.
     *
     * @param string $uuid
     * @return string
     */
    private function generateSubscriptionLink(string $uuid): string
    {
        $baseUrl = config('app.url');
        return "{$baseUrl}/subs/{$uuid}/modern";
    }

    /**
     * Create new user with XUI clients.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createUser(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'tg_id' => 'required|string|max:255',
                'comment' => 'required|string|max:500',
                'demo' => 'boolean',
                'expiration' => 'nullable|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            $tgId = $request->input('tg_id');
            $comment = $request->input('comment');
            $isDemo = $request->boolean('demo', false);
            $expiration = $request->input('expiration');
            $email = "client{$tgId}";

            // Check if user already exists
            $existingUser = User::where('tg_id', $tgId)
                ->orWhere('email', $email)
                ->first();

            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'User already exists',
                    'data' => [
                        'existing_user' => [
                            'id' => $existingUser->id,
                            'uuid' => $existingUser->uuid,
                            'email' => $existingUser->email,
                            'tg_id' => $existingUser->tg_id,
                        ]
                    ]
                ], 409);
            }

            DB::beginTransaction();

            try {
                // Create user
                $userData = [
                    'uuid' => Str::uuid()->toString(),
                    'tg_id' => $tgId,
                    'email' => $email,
                ];

                // Add demo_until if demo flag is set and expiration is provided
                if ($isDemo && $expiration) {
                    $userData['demo_until'] = $expiration > 9999999999
                        ? \Carbon\Carbon::createFromTimestampMs($expiration)
                        : \Carbon\Carbon::createFromTimestamp($expiration);
                }

                $user = User::create($userData);

                Log::info('User created', [
                    'user_id' => $user->id,
                    'uuid' => $user->uuid,
                    'tg_id' => $tgId,
                    'email' => $email,
                    'is_demo' => $isDemo,
                    'expiration' => $expiration,
                    'demo_until' => $user->demo_until
                ]);

                // Get all available servers and inbounds
                $servers = XuiServer::with('xuiInbounds')->get();
                $xuiApiService = new XuiApiService();

                $createdClients = [];
                $failedClients = [];

                foreach ($servers as $server) {
                    foreach ($server->xuiInbounds as $inbound) {
                        try {
                            // Create XuiClient record
                            $xuiClient = XuiClient::create([
                                'user_id' => $user->id,
                                'xui_server_id' => $server->id,
                                'xui_inbound_id' => $inbound->id,
                                'client_id' => $user->uuid,
                                'email' => $user->email,
                                'enable' => true,
                                'tg_id' => $tgId,
                                'sub_id' => "unlimited_{$tgId}",
                                'comment' => $comment,
                            ]);

                            // Create client on X-UI server
                            $success = $xuiApiService->createClient($server, $inbound, $user, $comment, $isDemo, $expiration);

                            if ($success) {
                                $createdClients[] = [
                                    'server' => $server->name,
                                    'inbound_id' => $inbound->inbound_id,
                                    'xui_client_id' => $xuiClient->id
                                ];
                            } else {
                                $failedClients[] = [
                                    'server' => $server->name,
                                    'inbound_id' => $inbound->inbound_id,
                                    'error' => 'Failed to create client on X-UI server'
                                ];
                            }

                        } catch (\Exception $e) {
                            $failedClients[] = [
                                'server' => $server->name,
                                'inbound_id' => $inbound->inbound_id ?? 'unknown',
                                'error' => $e->getMessage()
                            ];
                            Log::error('Failed to create XUI client', [
                                'user_id' => $user->id,
                                'server' => $server->name,
                                'inbound_id' => $inbound->inbound_id ?? 'unknown',
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }

                DB::commit();

                Log::info('User creation completed', [
                    'user_id' => $user->id,
                    'created_clients' => count($createdClients),
                    'failed_clients' => count($failedClients)
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'User created successfully',
                    'data' => [
                        'user' => [
                            'id' => $user->id,
                            'uuid' => $user->uuid,
                            'email' => $user->email,
                            'tg_id' => $user->tg_id,
                            'demo_until' => $user->demo_until,
                            'created_at' => $user->created_at,
                            'updated_at' => $user->updated_at,
                        ],
                        'subscription_link' => $this->generateSubscriptionLink($user->uuid),
                        'clients_created' => count($createdClients),
                        'clients_failed' => count($failedClients),
                        'created_clients' => $createdClients,
                        'failed_clients' => $failedClients,
                        'is_demo' => $isDemo,
                        'expiration' => $expiration
                    ]
                ], 201);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Error creating user', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get API information.
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'api_version' => '1.0',
                'endpoints' => [
                    'subscription_link' => [
                        'method' => 'POST',
                        'url' => '/api/subscription/link',
                        'description' => 'Get subscription link by user identifier',
                        'parameters' => [
                            'tg_id' => 'Telegram ID (optional)',
                            'email' => 'User email (optional)',
                            'client_id' => 'Client ID (optional)'
                        ],
                        'note' => 'At least one parameter must be provided'
                    ],
                    'create_user' => [
                        'method' => 'POST',
                        'url' => '/api/user/create',
                        'description' => 'Create new user with XUI clients',
                        'parameters' => [
                            'tg_id' => 'Telegram ID (required)',
                            'comment' => 'User comment (required)',
                            'demo' => 'Demo access flag (optional, default: false)',
                            'expiration' => 'Unix timestamp for expiration (optional)'
                        ]
                    ]
                ]
            ]
        ]);
    }
}
