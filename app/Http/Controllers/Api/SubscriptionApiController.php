<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\XuiServer;
use App\Models\SubscriptionExtension;
use App\Models\XuiInbound;
use App\Models\XuiClient;
use App\Services\XuiApiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class SubscriptionApiController extends Controller
{
    /**
     * Get subscription link by user identifier.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSubscriptionLink(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'uuid' => 'nullable|uuid',
                'tg_id' => 'nullable|string',
                'email' => 'nullable|email',
                'client_id' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Check that at least one identifier is provided
            $uuid = $request->input('uuid');
            $tgId = $request->input('tg_id');
            $email = $request->input('email');
            $clientId = $request->input('client_id');

            if (!$tgId && !$email && !$clientId) {
                return response()->json([
                    'success' => false,
                    'message' => 'At least one identifier (uuid, tg_id, email, or client_id) must be provided'
                ], 400);
            }

            // Find user by provided identifier
            $user = $this->findUserByIdentifier($uuid, $tgId, $email, $clientId);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            // Generate subscription link
            $subscriptionLink = $this->generateSubscriptionLink($user->uuid);

            Log::info('Subscription link requested', [
                'user_id' => $user->id,
                'uuid' => $user->uuid,
                'tg_id' => $tgId,
                'email' => $email,
                'client_id' => $clientId,
                'link' => $subscriptionLink
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription_link' => $subscriptionLink,
                    'uuid' => $user->uuid,
                    'user' => [
                        'id' => $user->id,
                        'email' => $user->email,
                        'tg_id' => $user->tg_id,
                        'created_at' => $user->created_at,
                        'updated_at' => $user->updated_at,
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting subscription link', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Find user by one of the provided identifiers.
     *
     * @param string|null $tgId
     * @param string|null $email
     * @param string|null $clientId
     * @return User|null
     */
    private function findUserByIdentifier(?string $uuid, ?string $tgId, ?string $email, ?string $clientId): ?User
    {
        // dd($uuid, $tgId, $email, $clientId);
        // Try to find by uuid
        if ($uuid) {
            $user = User::where('uuid', $uuid)->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by tg_id first
        if ($tgId) {
            $user = User::where('tg_id', $tgId)->first();
            if ($user) {
                return $user;
            }
            $user = User::where('email', "client{$tgId}")->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by email
        if ($email) {
            $user = User::where('email', $email)->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by client_id (assuming it's stored in email field with "client" prefix)
        if ($clientId) {
            $emailFromClientId = "client{$clientId}";
            $user = User::where('email', $emailFromClientId)->first();
            if ($user) {
                return $user;
            }
        }

        return null;
    }

    /**
     * Generate subscription link for user UUID.
     *
     * @param string $uuid
     * @return string
     */
    private function generateSubscriptionLink(string $uuid): string
    {
        $baseUrl = config('app.url');
        return "{$baseUrl}/subs/{$uuid}/modern";
    }

    /**
     * Create new user with XUI clients.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createUser(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'tg_id' => 'required|string|max:255',
                'comment' => 'nullable|string|max:500',
                'demo' => ['nullable', 'string', function ($attribute, $value, $fail) {
                    if (!in_array($value, ['true', 'false', '1', '0'], true)) {
                        $fail($attribute.' must be boolean-like string (true, false, 1, 0).');
                    }
                }],
                'expiration' => ['integer', 'min:1', function ($attribute, $value, $fail) use ($request) {
                    $demo = $request->input('demo');
                    // expiration обязателен, если demo НЕ true и НЕ 1
                    if (!in_array($demo, ['true', '1'], true) && ($value === null || $value === '')) {
                        $fail($attribute . ' is required when demo is not true or 1.');
                    }
                }],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            $tgId = $request->input('tg_id', null);
            $comment = $request->input('comment');
            $isDemo = $request->boolean('demo', false);
            $expiration = $request->input('expiration');
            $email = "client{$tgId}";

            // Check if user already exists
            $existingUser = User::where('tg_id', $tgId)
                ->orWhere('email', $email)
                ->first();

            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'User already exists',
                    'data' => [
                        'existing_user' => [
                            'id' => $existingUser->id,
                            'uuid' => $existingUser->uuid,
                            'email' => $existingUser->email,
                            'tg_id' => $existingUser->tg_id,
                        ]
                    ]
                ], 409);
            }

            DB::beginTransaction();

            try {
                // Calculate expiry_time based on priority: expiration > demo flag > default
                $expiryTime = null;
                if ($expiration) {
                    // If expiration is provided, use it (ignore demo flag)
                    $expiryTime = $expiration > 9999999999
                        ? \Carbon\Carbon::createFromTimestampMs($expiration)
                        : \Carbon\Carbon::createFromTimestamp($expiration);
                } else {
                    // If no expiration provided, use demo flag or default
                    if ($isDemo) {
                        // Demo: 1 day
                        $expiryTime = now()->addDay();
                    } else {
                        // Regular: 1 week
                        $expiryTime = now()->addWeek();
                    }
                }

                // Create user
                $userData = [
                    'uuid' => Str::uuid()->toString(),
                    'tg_id' => $tgId,
                    'email' => $email,
                    'comment' => $comment,
                    'expiry_time' => $expiryTime,
                    'expired' => false,
                    'disabled_at' => null,
                    'total_gb' => 0, // Was 1GB for demo, but now is unlimited for regular and demo as well
                    'used_gb' => 0,
                    'up_traffic' => 0,
                    'down_traffic' => 0,
                ];

                // Add demo_until if demo flag is set and expiration is provided
                if ($isDemo) {
                    $userData['demo_until'] = $expiryTime;
                }

                $user = User::updateOrCreate([
                    'tg_id' => $tgId,
                    'email' => $email,
                ], $userData);

                Log::info('User created', [
                    'user_id' => $user->id,
                    'uuid' => $user->uuid,
                    'tg_id' => $tgId,
                    'email' => $email,
                    'is_demo' => $isDemo,
                    'expiration' => $user->demo_until,
                    'demo_until' => $user->demo_until,
                ]);

                // Get all available servers and inbounds
                $servers = XuiServer::with('xuiInbounds')->get();
                $xuiApiService = new XuiApiService();

                $createdClients = [];
                $failedClients = [];

                foreach ($servers as $server) {
                    foreach ($server->xuiInbounds as $inbound) {
                        try {
                            // Generate unique email for this inbound: client{tg_id}_{inbound_id}
                            $clientEmail = "{$user->email}_{$inbound->inbound_id}";

                            // Create XuiClient record with data from User
                            $xuiClient = XuiClient::create([
                                'user_id' => $user->id,
                                'xui_server_id' => $server->id,
                                'xui_inbound_id' => $inbound->id,
                                'client_id' => $user->uuid,
                                'email' => $clientEmail,
                                'enable' => !$user->expired && !$user->disabled_at,
                                'disabled_at' => $user->disabled_at,
                                'flow' => 'xtls-rprx-vision',
                                'tg_id' => $user->tg_id,
                                'sub_id' => "unlimited_{$user->tg_id}",
                                'comment' => $user->comment,
                                'expiry_time' => $user->expiry_time,
                                'expired' => $user->expired,
                                'total_gb' => $user->total_gb,
                                'used_gb' => $user->used_gb,
                                'up_traffic' => $user->up_traffic,
                                'down_traffic' => $user->down_traffic,
                            ]);

                            // Create client on X-UI server with unique email
                            $success = $xuiApiService->createClient($server, $inbound, $user, $clientEmail, $user->comment, $isDemo, $user->expiry_time);

                            if ($success) {
                                $createdClients[] = [
                                    'server' => $server->name,
                                    'inbound_id' => $inbound->inbound_id,
                                    'xui_client_id' => $xuiClient->id
                                ];
                            } else {
                                $failedClients[] = [
                                    'server' => $server->name,
                                    'inbound_id' => $inbound->inbound_id,
                                    'error' => 'Failed to create client on X-UI server'
                                ];
                            }

                        } catch (\Exception $e) {
                            $failedClients[] = [
                                'server' => $server->name,
                                'inbound_id' => $inbound->inbound_id ?? 'unknown',
                                'error' => $e->getMessage()
                            ];
                            Log::error('Failed to create XUI client', [
                                'user_id' => $user->id,
                                'server' => $server->name,
                                'inbound_id' => $inbound->inbound_id ?? 'unknown',
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }

                DB::commit();

                Log::info('User creation completed', [
                    'user_id' => $user->id,
                    'created_clients' => count($createdClients),
                    'failed_clients' => count($failedClients)
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'User created successfully',
                    'data' => [
                        'user' => [
                            'id' => $user->id,
                            'uuid' => $user->uuid,
                            'email' => $user->email,
                            'tg_id' => $user->tg_id,
                            'demo_until' => $user->demo_until,
                            'created_at' => $user->created_at,
                            'updated_at' => $user->updated_at,
                        ],
                        'subscription_link' => $this->generateSubscriptionLink($user->uuid),
                        'clients_created' => count($createdClients),
                        'clients_failed' => count($failedClients),
                        'created_clients' => $createdClients,
                        'failed_clients' => $failedClients,
                        'is_demo' => $isDemo,
                        'expiration' => $expiration
                    ]
                ], 201);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Error creating user', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get API information.
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'api_version' => '1.0',
                'endpoints' => [
                    'subscription_link' => [
                        'method' => 'POST',
                        'url' => '/api/subscription/link',
                        'description' => 'Get subscription link by user identifier',
                        'parameters' => [
                            'tg_id' => 'Telegram ID (optional)',
                            'email' => 'User email (optional)',
                            'client_id' => 'Client ID (optional)'
                        ],
                        'note' => 'At least one parameter must be provided'
                    ],
                    'create_user' => [
                        'method' => 'POST',
                        'url' => '/api/user/create',
                        'description' => 'Create new user with XUI clients',
                        'parameters' => [
                            'tg_id' => 'Telegram ID (required)',
                            'comment' => 'User comment (required)',
                            'demo' => 'Demo access flag (optional, default: false)',
                            'expiration' => 'Unix timestamp for expiration (optional)'
                        ]
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get user statistics by tg_id, uuid, or email
     */
    public function getUserStats(Request $request)
    {
        try {
            // Validate request - at least one identifier is required
            $request->validate([
                'tg_id' => 'nullable|string',
                'uuid' => 'nullable|string',
                'email' => 'nullable|string',
            ]);

            // Check that at least one identifier is provided
            if (!$request->tg_id && !$request->uuid && !$request->email) {
                return response()->json([
                    'success' => false,
                    'message' => 'At least one of tg_id, uuid, or email is required',
                ], 422);
            }

            // Find user by provided identifier
            $user = null;
            if ($request->tg_id) {
                $user = User::where('tg_id', $request->tg_id)->first();
            } elseif ($request->uuid) {
                $user = User::where('uuid', $request->uuid)->first();
            } elseif ($request->email) {
                $user = User::where('email', $request->email)->first();
            }

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            // Get user's clients with server and inbound information
            $clients = $user->xuiClients()
                ->with(['xuiServer', 'xuiInbound'])
                ->whereHas('xuiServer', function ($query) {
                    $query->where('is_active', true);
                })
                ->get();

            // Build servers array with inbound IDs
            $servers = [];
            foreach ($clients as $client) {
                $serverName = $client->xuiServer->name;
                $serverHost = $client->xuiServer->host;
                if (!isset($servers[$serverHost])) {
                    $servers[$serverHost] = [];
                }
                if (!in_array($client->xuiInbound->inbound_id, $servers[$serverHost])) {
                    $servers[$serverHost][] = $client->xuiInbound->inbound_id;
                }
            }

            // Calculate statistics
            $now = now();
            $isExpired = $user->expiry_time && $user->expiry_time->isPast();
            $isDemo = !is_null($user->demo_until);
            $usedTraffic = $user->up_traffic + $user->down_traffic;
            $startedUsing = $usedTraffic > 0;
            $daysSinceCreation = $user->created_at->diffInDays($now);
            $dailyAverage = $daysSinceCreation > 0 ? $usedTraffic / $daysSinceCreation : 0;

            // Calculate remaining traffic and usage percentage
            $remainingTraffic = null;
            $usagePercentage = 0;
            if ($user->total_gb > 0) {
                $remainingTraffic = max(0, $user->total_gb - $usedTraffic);
                $usagePercentage = round(($usedTraffic / $user->total_gb) * 100, 2);
            }

            // Calculate days until expiry
            $daysUntilExpiry = null;
            if ($user->expiry_time) {
                $daysUntilExpiry = $user->expiry_time->diffInDays($now, false);
                if ($daysUntilExpiry < 0) {
                    $daysUntilExpiry = 0; // Already expired
                }
            }

            // Determine status
            $status = 'active';
            if ($user->disabled_at) {
                $status = 'disabled';
            } elseif ($isExpired) {
                $status = 'expired';
            } elseif ($isDemo) {
                $status = 'demo';
            }

            // Client statistics
            $activeClients = $clients->filter(function ($client) {
                return $client->enable && !$client->disabled_at && !$client->expired;
            })->count();

            $disabledClients = $clients->filter(function ($client) {
                return !$client->enable || $client->disabled_at;
            })->count();

            $expiredClients = $clients->filter(function ($client) {
                return $client->expired;
            })->count();

            // Get last activity from clients
            $lastActivity = $clients->max('updated_at');

            // Calculate can_connect after we have activeClients count
            $canConnect = !$isExpired && !$user->disabled_at && $activeClients > 0;

            $statsData = [
                'server_time' => now()->toISOString(),
                // Basic user info
                'tg_id' => $user->tg_id,
                'uuid' => $user->uuid,
                'email' => $user->email,
                'comment' => $user->comment,
                'created_at' => $user->created_at->toISOString(),
                'updated_at' => $user->updated_at->toISOString(),

                // Server and connection info
                'servers' => $servers,
                'enable' => !$isExpired && !$user->disabled_at,
                'can_connect' => $canConnect,

                // Traffic statistics (from users table)
                'up' => $user->up_traffic,
                'down' => $user->down_traffic,
                'total' => $user->total_gb,
                'used_traffic' => $usedTraffic,
                'remaining_traffic' => $remainingTraffic,
                'usage_percentage' => $usagePercentage,
                'daily_average' => round($dailyAverage),

                // Time and expiry info
                'expiry_time' => $user->expiry_time ? $user->expiry_time->timestamp * 1000 : 0, // milliseconds
                'days_until_expiry' => $daysUntilExpiry,
                'is_expired' => $isExpired,
                'expired' => $user->expired,
                'disabled_at' => $user->disabled_at?->toISOString(),

                // Demo and status info
                'is_demo' => $isDemo,
                'demo_until' => $user->demo_until?->toISOString(),
                'status' => $status,
                'started_using' => $startedUsing,
                'needs_renewal' => $isExpired || ($daysUntilExpiry !== null && $daysUntilExpiry <= 3),

                // Client statistics
                'total_clients' => $clients->count(),
                'active_clients' => $activeClients,
                'disabled_clients' => $disabledClients,
                'expired_clients' => $expiredClients,

                // Additional info
                'days_since_creation' => $daysSinceCreation,
                'last_activity' => $lastActivity?->toISOString(),
            ];

            Log::info('User stats retrieved', [
                'user_id' => $user->id,
                'tg_id' => $user->tg_id,
                'status' => $status,
                'used_traffic_gb' => round($usedTraffic / 1024 / 1024 / 1024, 2),
            ]);

            return response()->json([
                'success' => true,
                'data' => $statsData,
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to get user stats', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get user stats',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Extend user subscription.
     */
    public function extendSubscription(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'tg_id' => 'nullable|string',
                'uuid' => 'nullable|string',
                'email' => 'nullable|string',
                'expiration' => 'required|integer|min:0',
                'comment' => 'nullable|string|max:1000',
                'source' => 'nullable|string|max:100',
            ]);

            // Check that at least one identifier is provided
            if (!$request->tg_id && !$request->uuid && !$request->email) {
                return response()->json([
                    'success' => false,
                    'message' => 'At least one of tg_id, uuid, or email is required',
                ], 422);
            }

            // Find user by provided identifier
            $user = null;
            if ($request->tg_id) {
                $user = User::where('tg_id', $request->tg_id)->first();
            } elseif ($request->uuid) {
                $user = User::where('uuid', $request->uuid)->first();
            } elseif ($request->email) {
                $user = User::where('email', $request->email)->first();
            }

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            // Convert Unix timestamp to Carbon instance
            $newExpiryTime = $request->expiration > 9999999999
                        ? \Carbon\Carbon::createFromTimestampMs($request->expiration)
                        : \Carbon\Carbon::createFromTimestamp($request->expiration);
            $oldExpiryTime = $user->expiry_time;

            // Validate that new expiry time is in the future
            if ($newExpiryTime->isPast()) {
                return response()->json([
                    'success' => false,
                    'message' => 'New expiry time must be in the future',
                ], 422);
            }

            // Start database transaction
            DB::beginTransaction();

            try {
                // Update user expiry time
                $user->update([
                    'expiry_time' => $newExpiryTime,
                    'expired' => false, // Reset expired flag
                ]);

                // Create extension history record
                $extension = SubscriptionExtension::create([
                    'user_id' => $user->id,
                    'old_expiry_time' => $oldExpiryTime,
                    'new_expiry_time' => $newExpiryTime,
                    'source' => $request->source ?? 'api',
                    'comment' => $request->comment,
                    'request_ip' => $request->ip(),
                    'request_user_agent' => $request->userAgent(),
                    'additional_data' => [
                        'request_data' => $request->only(['tg_id', 'uuid', 'email']),
                        'user_agent' => $request->userAgent(),
                        'timestamp' => now()->toISOString(),
                    ],
                ]);

                // Update expiry time on XUI servers
                $clients = $user->xuiClients()->with(['xuiServer', 'xuiInbound'])->get();
                $updatedServers = [];
                $failedServers = [];

                foreach ($clients as $client) {
                    $server = $client->xuiServer;
                    $inbound = $client->xuiInbound;

                    if (!$server->is_active) {
                        continue;
                    }

                    try {
                        $xuiApiService = new \App\Services\XuiApiService();

                        // Update client expiry time on XUI server
                        $success = $xuiApiService->updateClientExpiryTime(
                            $server,
                            $inbound,
                            $client->client_id,
                            $newExpiryTime
                        );

                        if ($success) {
                            $updatedServers[] = $server->name;

                            // Update local client record
                            $client->update([
                                'expiry_time' => $newExpiryTime,
                                'expired' => false,
                            ]);
                        } else {
                            $failedServers[] = $server->name;
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to update client on XUI server', [
                            'server' => $server->name,
                            'client_id' => $client->client_id,
                            'error' => $e->getMessage(),
                        ]);
                        $failedServers[] = $server->name;
                    }
                }

                // Commit transaction
                DB::commit();

                Log::info('Subscription extended successfully', [
                    'user_id' => $user->id,
                    'uuid' => $user->uuid,
                    'tg_id' => $user->tg_id,
                    'old_expiry' => $oldExpiryTime?->toISOString(),
                    'new_expiry' => $newExpiryTime->toISOString(),
                    'source' => $request->source ?? 'api',
                    'comment' => $request->comment,
                    'updated_servers' => $updatedServers,
                    'failed_servers' => $failedServers,
                    'extension_id' => $extension->id,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Subscription extended successfully',
                    'data' => [
                        'user_id' => $user->id,
                        'uuid' => $user->uuid,
                        'tg_id' => $user->tg_id,
                        'old_expiry_time' => $oldExpiryTime?->toISOString(),
                        'new_expiry_time' => $newExpiryTime->toISOString(),
                        'extension_days' => $extension->extension_days,
                        'extension_duration' => $extension->extension_duration,
                        'updated_servers' => $updatedServers,
                        'failed_servers' => $failedServers,
                        'extension_id' => $extension->id,
                    ],
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to extend subscription', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to extend subscription',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
