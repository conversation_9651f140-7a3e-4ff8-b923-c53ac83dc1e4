<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Announce;
use App\Services\SubscriptionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Get subscription by UUID.
     */
    public function show(Request $request, string $uuid): Response
    {
        $startTime = microtime(true);

        try {
            // Validate UUID format early
            if (!$this->isValidUuid($uuid)) {
                Log::warning("Invalid UUID format: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                return response('Invalid UUID format', 400);
            }

            // Check if user exists first (fast DB query)
            $user = User::findByUuid($uuid);
            if (!$user) {
                Log::info("User not found for UUID: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                return response('User not found', 404);
            }
            // dd($user);

            // Use cache for frequently requested subscriptions (5 minutes TTL)
            // $cacheKey = "subscription:{$uuid}";
            // $vlessContent = Cache::remember($cacheKey, 300, function () use ($user) {
            //     return $this->generateVlessContent($user);
            // });
            // кэширование больше не нужно, должны отдавать всегда свежие данные

            $vlessContent = $this->generateVlessContent($user);

            // это не нужно, так как сервер x-ui сам блокирует подключение к vless
            // if (!$vlessContent) {
            //     Log::info("No active subscriptions found for UUID: {$uuid}", [
            //         'ip' => $request->ip(),
            //         'user_agent' => $request->userAgent(),
            //         'user_id' => $user->id,
            //     ]);
            //     return response('No active subscriptions found', 404);
            // }

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info("Subscription delivered for UUID: {$uuid}", [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => $user->id,
                'content_length' => strlen($vlessContent),
                'response_time_ms' => $responseTime,
                // 'cached' => Cache::has($cacheKey),
            ]);

            // Check if request is from a browser
            if ($this->isBrowserUserAgent($request)) {
                // Return HTML view for browser requests
                return $this->renderSubscriptionView($user, $vlessContent, $uuid);
            }

            // Return vless content with appropriate headers for V2Ray clients
            return response($vlessContent, 200, $this->getSubscriptionHeaders($uuid));

        } catch (\Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::error("Error delivering subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'response_time_ms' => $responseTime,
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Show modern subscription page for a user.
     */
    public function showModern(Request $request, string $uuid): Response
    {
        try {
            if (!$this->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            // Generate vless content
            $vlessContent = $this->generateVlessContent($user);

            // Always render modern view for this endpoint
            return $this->renderModernSubscriptionView($user, $vlessContent, $uuid);

        } catch (\Exception $e) {
            Log::error("Error getting modern subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Show modern subscription page without QR code for a user.
     */
    public function showModernNoQR(Request $request, string $uuid): Response
    {
        try {
            if (!$this->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            // Generate vless content
            $vlessContent = $this->generateVlessContent($user);

            // Render modern view without QR
            return $this->renderModernNoQRSubscriptionView($user, $vlessContent, $uuid);

        } catch (\Exception $e) {
            Log::error("Error getting modern no-QR subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Get user statistics (for debugging/monitoring).
     */
    public function stats(Request $request, string $uuid): JsonResponse|Response
    {
        try {
            if (!$this->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $stats = $this->subscriptionService->getUserStats($uuid);

            if (!$stats) {
                return response('User not found', 404);
            }

            return response()->json($stats);

        } catch (\Exception $e) {
            Log::error("Error getting stats for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Generate vless content from user's clients.
     */
    private function generateVlessContent(User $user): string
    {
        // Get ALL clients for this user from all servers and inbounds (including expired)
        $clients = $user->xuiClients()
            ->with(['xuiInbound.xuiServer'])
            ->whereHas('xuiServer', function ($query) {
                $query->where('is_active', true);
            })
            ->whereHas('xuiInbound', function ($query) {
                $query->where('enable', true);
            })
            // ->where('enable', true)
            // ->whereNull('disabled_at')
            // Remove expired filter - return content for all clients
            ->get();

        // dd($clients);

        if ($clients->isEmpty()) {
            return '';
        }

        $vlessLinks = [];
        $totalUpload = 0;
        $totalDownload = 0;
        $totalLimit = 0;

        foreach ($clients as $client) {
            try {

                // Generate vless URL from stored inbound data
                $vlessUrl = $client->generateVlessUrl();

                if ($vlessUrl) {
                    $vlessLinks[] = $vlessUrl;

                    // Accumulate usage stats for headers
                    $totalUpload += $client->up_traffic;
                    $totalDownload += $client->down_traffic;
                    $totalLimit += $client->total_gb;
                }
            } catch (\Exception $e) {
                Log::error("Error generating vless URL for client {$client->client_id}", [
                    'server' => $client->xuiServer->name ?? 'unknown',
                    'inbound' => $client->xuiInbound->remark ?? 'unknown',
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if (empty($vlessLinks)) {
            return '';
        }

        // Return vless URLs separated by newlines
        return implode("\n", $vlessLinks);
    }



    private function getSubscriptionTitle(?string $uuid = null): string
    {
        $title = config("app.subs_title", "no-title");

        if ($uuid) {
            $user = User::findByUuid($uuid);
            if ($user) {
                $userClientId = str_replace("client", "", $user->email);
                $title .= " \n       ID клиента: {$userClientId} 🔰";
            }
        }

        return $title;
    }

    private function getSubscriptionAnnounce(): string
    {
        return config("app.subs_announce", "");
    }

    /**
     * Get announce data based on subscription expiry status.
     */
    private function getAnnounceData(?string $uuid = null): array
    {
        // Default fallback
        $defaultText = $this->getSubscriptionAnnounce();
        $defaultUrl = config("app.subs_announce_url", "");

        if (!$uuid) {
            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];
        }

        try {
            $user = User::findByUuid($uuid);
            if (!$user) {
                return [
                    'text' => $defaultText,
                    'url' => $defaultUrl,
                ];
            }

            // Get user's clients to check expiry (including expired ones)
            $clients = $user->xuiClients()
                ->whereHas('xuiServer', function ($query) {
                    $query->where('is_active', true);
                })
                ->whereHas('xuiInbound', function ($query) {
                    $query->where('enable', true);
                })
                ->where('enable', true)
                ->whereNull('disabled_at')
                // Remove expired filter to include expired clients for announce logic
                ->get();

            // Get earliest expiry time from all clients
            $earliestExpiry = $clients->filter(fn($client) => $client->expiry_time)
                ->min('expiry_time');

            if ($earliestExpiry) {
                $now = Carbon::now();
                $expiryTime = Carbon::parse($earliestExpiry);

                // Check if subscription has already expired
                if ($expiryTime->isPast()) {

                    if ($user->demo_until) {
                        return [
                            'text' => "❗️ #c11e14ДЕМО-ВЕРСИЯ ЗАКОНЧИЛАСЬ ❗️\n\nНажмите сюда, чтобы продлить подписку 💳",
                            'url' => config('app.url') . "/renew?uuid=" . $uuid,
                        ];
                    }

                    return [
                        'text' => "❗️ #c11e14ПОДПИСКА #c11e14ИСТЕКЛА ❗️\n\nНажмите сюда, чтобы продлить подписку 💳",
                        'url' => config('app.url') . "/renew?uuid=" . $uuid,
                    ];
                }

                // Check if expiry is within 3 days
                if ($expiryTime->isFuture() && $now->diffInDays($expiryTime, false) < 3) {
                    // Calculate remaining time
                    $remainingTime = $this->formatRemainingTime($now, $expiryTime);

                    if ($user->demo_until) {
                        return [
                            'text' => "🪫 #c11e14ДЕМО-ВЕРСИЯ ЗАКОНЧИТСЯ через {$remainingTime}\n\n#c11e14Нажмите сюда, чтобы продлить подписку 💳",
                            'url' => config('app.url') . "/renew?uuid=" . $uuid,
                        ];
                    }

                    return [
                        'text' => "🪫 ПОДПИСКА #c11e14ЗАКОНЧИТСЯ через {$remainingTime}\n\n#c11e14Нажмите сюда, чтобы продлить подписку 💳",
                        'url' => config('app.url') . "/renew?uuid=" . $uuid,
                    ];
                }
            }

            // If not expiring soon, get latest announce from database
            $latestAnnounce = Announce::getLatest();

            if ($latestAnnounce) {
                return [
                    'text' => $latestAnnounce->message,
                    'url' => $latestAnnounce->url ?: $defaultUrl,
                ];
            }

            // Fallback to default
            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];

        } catch (\Exception $e) {
            Log::error("Error getting announce data for UUID: {$uuid}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];
        }
    }

    /**
     * Format remaining time in a human-readable format.
     */
    private function formatRemainingTime(Carbon $now, Carbon $expiry): string
    {
        $diff = $now->diff($expiry);

        if ($diff->d > 0) {
            return $diff->d . ' ' . $this->pluralize($diff->d, 'день', 'дня', 'дней');
        } elseif ($diff->h > 0) {
            return $diff->h . ' ' . $this->pluralize($diff->h, 'час', 'часа', 'часов')
                . ' ' . $diff->i . ' ' . $this->pluralize($diff->i, 'минута', 'минуты', 'минут');
        } else {
            return $diff->i . ' ' . $this->pluralize($diff->i, 'минута', 'минуты', 'минут');
        }
    }

    /**
     * Russian pluralization helper.
     */
    private function pluralize(int $number, string $one, string $few, string $many): string
    {
        $mod10 = $number % 10;
        $mod100 = $number % 100;

        if ($mod100 >= 11 && $mod100 <= 19) {
            return $many;
        }

        if ($mod10 == 1) {
            return $one;
        }

        if ($mod10 >= 2 && $mod10 <= 4) {
            return $few;
        }

        return $many;
    }

    private function getRoutingRules(): string
    {
        return <<<JSON
{"domainStrategy":"AsIs","id":"1EAA88BB-B5F5-4F69-82D0-9FF449908794","balancers":[],"domainMatcher":"hybrid","rules":[{"domain":["regexp:.*\\\\.ru$","geosite:category-ru"],"id":"9CA62C69-3D7A-4FE5-9E19-2189E2E4853E","outboundTag":"direct","type":"field","__name__":"Direct Russia","ip":["geoip:ru"]}],"name":"Example"}
JSON;
    }

    /**
     * Get subscription headers.
     */
    private function getSubscriptionHeaders(?string $uuid = null): array
    {
        // Get announce text and URL based on subscription expiry
        $announceData = $this->getAnnounceData($uuid);

        $headers = [
            'Profile-Title' => 'base64:' . base64_encode($this->getSubscriptionTitle($uuid)),
            'Content-Type' => 'text/plain; charset=utf-8',
            'Profile-Update-Interval' => '1', // update interval in hours
            'Profile-Web-Page-Url' => config('app.url') . '/' . $uuid,
            'Support-Url' => config("app.subs_support_url"),
            'Announce' => 'base64:' . base64_encode($announceData['text']),
            'Announce-Url' => $announceData['url'],
            'Update-always' => 'true',
            // 'Routing' => base64_encode($this->getRoutingRules()),
        ];

        // Generate real-time usage stats
        if ($uuid) {
            $user = User::findByUuid($uuid);
            if ($user) {
                $clients = $user->xuiClients()
                    ->whereHas('xuiServer', function ($query) {
                        $query->where('is_active', true);
                    })
                    ->whereHas('xuiInbound', function ($query) {
                        $query->where('enable', true);
                    })
                    ->where('enable', true)
                    ->whereNull('disabled_at')
                    // Remove expired filter to include all clients for headers
                    ->get();

                // Get traffic data from User model (centralized source)
                $totalUpload = $user->up_traffic;
                $totalDownload = $user->down_traffic;
                $totalLimit = $user->total_gb;

                // If there is no traffic consumption at all, then we will specify at least 1 byte so that the traffic scale (traffic progress bar) starts to be displayed in the v2raytun applications
                if ($totalUpload === 0 && $totalDownload === 0) {
                    $totalDownload = 1;
                }

                // Get expiry time from User model (centralized source)
                $earliestExpiry = $user->expiry_time;

                // Build userinfo string
                $userinfo = sprintf('upload=%d; download=%d', $totalUpload, $totalDownload);

                // Add total only if there are actual limits set
                if ($totalLimit > 0) {
                    $userinfo .= sprintf('; total=%d', $totalLimit);
                }

                // Always add expire if there's an expiry time (even if expired)
                if ($earliestExpiry) {
                    $expireTimestamp = $earliestExpiry->timestamp;
                    $userinfo .= sprintf('; expire=%d', $expireTimestamp);
                }

                $headers['Subscription-Userinfo'] = $userinfo;
            }
        }

        return $headers;
    }

    private function isBrowserUserAgent(Request $request): bool
    {
        $userAgent = $request->userAgent() ?? '';
        $userAgentLower = strtolower($userAgent);

        // НЕ-браузеры: v2ray-клиенты и технические HTTP-клиенты
        $nonBrowserKeywords = [
            // v2ray и его производные
            'xray',
            'v2ray',
            'v2raya',
            'v2raytun',
            '2rayng',
            '2rayu',
            'hiddify',
            'v3ray',
            'sing-box',
            'happ',
            // тех. клиенты
            'curl',
            'wget',
            'httpclient',
            'python-requests',
            'go-http-client',
            'okhttp',
            'postman',
            'axios',
            'node-fetch',
            'java/', // часто в UA от Java-клиентов
            'libwww',
            'perl',
        ];

        // Браузеры — определяем по наличию типичных признаков
        $browserKeywords = [
            'mozilla/',     // общий идентификатор, почти все браузеры
            'chrome/',      // Chrome и Edge
            'safari/',      // Safari
            'firefox/',     // Firefox
            'opera/',       // Opera
            'edg/',         // Microsoft Edge
            'instagram',    // WebView Instagram
            'facebook',     // Facebook in-app browser
            'telegram',     // Telegram in-app browser
            'whatsapp',     // WhatsApp WebView
            'tiktok',       // TikTok in-app browser
        ];

        // Если user-agent пустой, считаем это не браузером
        if (empty($userAgentLower)) {
            return false;
        }

        // Если содержит один из НЕ-браузерных сигнатур — это не браузер
        foreach ($nonBrowserKeywords as $keyword) {
            if (strpos($userAgentLower, $keyword) !== false) {
                return false;
            }
        }

        // Если содержит хотя бы один браузерный признак — это браузер
        foreach ($browserKeywords as $keyword) {
            if (strpos($userAgentLower, $keyword) !== false) {
                return true;
            }
        }

        // Всё остальное — тоже не браузер
        return false;
    }

    /**
     * Render subscription view for browser requests.
     */
    private function renderSubscriptionView(User $user, string $vlessContent, string $uuid): Response
    {
        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get ALL user's clients for statistics (including expired ones for display)
        $clients = $user->xuiClients()
            ->whereHas('xuiServer', fn($q) => $q->where('is_active', true))
            ->whereHas('xuiInbound', fn($q) => $q->where('enable', true))
            ->where('enable', true)
            ->whereNull('disabled_at')
            // Don't filter by 'expired' for browser view - show all clients
            ->get();

        // Get statistics from User model (centralized source)
        $totalUpload = $user->up_traffic;
        $totalDownload = $user->down_traffic;
        $totalLimit = $user->total_gb;
        $earliestExpiry = $user->expiry_time;

        // Determine status
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time with appropriate units
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);

        // Prepare data for view
        $data = [
            'email' => $user->email ?? 'User',
            'subscription_url' => config('app.url') . '/subs/' . $user->uuid,
            'vless_links' => $vlessLinks,
            'up' => $totalUpload,
            'down' => $totalDownload,
            'total' => $totalLimit ?: 0,
            'used_traffic' => $totalUpload + $totalDownload,
            'data_limit' => $totalLimit ?: 0,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'uuid' => $uuid,
        ];

        return response()->view('subs.subscription', compact('data'));
    }

    /**
     * Render modern subscription view for browser requests.
     */
    private function renderModernSubscriptionView(User $user, string $vlessContent, string $uuid): Response
    {
        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get ALL user's clients for statistics (including expired ones for display)
        $clients = $user->xuiClients()
            ->whereHas('xuiServer', fn($q) => $q->where('is_active', true))
            ->whereHas('xuiInbound', fn($q) => $q->where('enable', true))
            ->where('enable', true)
            ->whereNull('disabled_at')
            // Don't filter by 'expired' for browser view - show all clients
            ->get();

        // Get statistics from User model (centralized source)
        $totalUpload = $user->up_traffic;
        $totalDownload = $user->down_traffic;
        $totalLimit = $user->total_gb;
        $earliestExpiry = $user->expiry_time;

        // Determine status
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time with appropriate units
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);

        // Prepare data for modern view
        $data = [
            'client_id' => str_replace("client", "", $user->email),
            'email' => $user->email ?? 'User',
            'subscription_url' => config('app.url') . '/subs/' . $user->uuid,
            'vless_links' => $vlessLinks,
            'vless_content' => $vlessContent,
            'up' => $totalUpload,
            'down' => $totalDownload,
            'total' => $totalLimit ?: 0,
            'used_traffic' => $totalUpload + $totalDownload,
            'data_limit' => $totalLimit ?: 0,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'uuid' => $uuid,
        ];

        return response()->view('subs.subscription-modern-simple', compact('data'));
    }

    /**
     * Render modern subscription view without QR for browser requests.
     */
    private function renderModernNoQRSubscriptionView(User $user, string $vlessContent, string $uuid): Response
    {
        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get ALL user's clients for statistics (including expired ones for display)
        $clients = $user->xuiClients()
            ->whereHas('xuiServer', fn($q) => $q->where('is_active', true))
            ->whereHas('xuiInbound', fn($q) => $q->where('enable', true))
            ->where('enable', true)
            ->whereNull('disabled_at')
            ->get();

        // Calculate statistics
        $totalUpload = $clients->sum('up_traffic');
        $totalDownload = $clients->sum('down_traffic');
        $totalLimit = $clients->filter(fn($client) => $client->total_gb > 0)->sum('total_gb');
        $earliestExpiry = $clients->filter(fn($client) => $client->expiry_time)->min('expiry_time');

        // Determine status
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time with appropriate units
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);

        // Prepare data for modern view without QR
        $data = [
            'email' => $user->email ?? 'User',
            'subscription_url' => config('app.url') . '/subs/' . $user->uuid,
            'vless_links' => $vlessLinks,
            'vless_content' => $vlessContent,
            'up' => $totalUpload,
            'down' => $totalDownload,
            'total' => $totalLimit ?: 0,
            'used_traffic' => $totalUpload + $totalDownload,
            'data_limit' => $totalLimit ?: 0,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'uuid' => $uuid,
        ];

        return response()->view('subs.subscription-modern-no-qr', compact('data'));
    }

    /**
     * Force refresh subscription content for a user.
     */
    public function refresh(Request $request, string $uuid): JsonResponse|Response
    {
        try {
            if (!$this->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            // Clear cache for this user
            Cache::forget("subscription:{$uuid}");

            // Force refresh by syncing all servers that have this user's clients
            $refreshedCount = 0;
            $servers = $user->xuiClients()
                ->with('xuiServer')
                ->get()
                ->pluck('xuiServer')
                ->unique('id');

            foreach ($servers as $server) {
                try {
                    $this->subscriptionService->syncServerSubscriptions($server);
                    $refreshedCount++;
                } catch (\Exception $e) {
                    Log::warning("Failed to sync server {$server->name}", [
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info("Forced refresh for UUID: {$uuid}", [
                'ip' => $request->ip(),
                'user_id' => $user->id,
                'refreshed_servers' => $refreshedCount,
                'total_servers' => $servers->count(),
            ]);

            return response()->json([
                'message' => 'Subscription content refreshed',
                'refreshed_servers' => $refreshedCount,
                'total_servers' => $servers->count(),
                'total_clients' => $user->xuiClients->count(),
            ]);

        } catch (\Exception $e) {
            Log::error("Error refreshing subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Validate UUID format.
     */
    private function isValidUuid(string $uuid): bool
    {
        return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $uuid);
    }

    /**
     * Calculate remaining time with appropriate units.
     */
    private function calculateRemainingTime($expiryTime): array
    {
        if (!$expiryTime || $expiryTime->isPast()) {
            return [
                'value' => 0,
                'unit' => 'expired',
                'display' => 'Истекла',
                'display_en' => 'Expired'
            ];
        }

        $now = now();
        $diff = $now->diff($expiryTime);

        $totalHours = $diff->days * 24 + $diff->h;

        // If less than 1 hour, show minutes
        if ($totalHours < 1) {
            $minutes = $diff->i;
            return [
                'value' => $minutes,
                'unit' => 'minutes',
                'display' => $minutes . ' мин.',
                'display_en' => $minutes . ' min.'
            ];
        }

        // If less than 24 hours, show hours and minutes
        if ($diff->days < 1) {
            $hours = $totalHours;
            $minutes = $diff->i;

            if ($minutes > 0) {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч. ' . $minutes . ' мин.',
                    'display_en' => $hours . ' h. ' . $minutes . ' min.'
                ];
            } else {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч.',
                    'display_en' => $hours . ' h.'
                ];
            }
        }

        // Otherwise show days
        return [
            'value' => $diff->days,
            'unit' => 'days',
            'display' => $diff->days . ' дн.',
            'display_en' => $diff->days . ' days'
        ];
    }
}
