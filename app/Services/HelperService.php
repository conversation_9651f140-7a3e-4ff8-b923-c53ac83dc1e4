<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\Request;

class HelperService
{

    public function getCompiledTextVariables($string, $user)
    {
        return str_replace(
            [
                '{uuid}',
                '{client_id}',
                '{host}',
                '{origin}',
                '{app_url}',
            ],
            [
                $user->uuid,
                $this->getCurrentUserClientId($user),
                request()->getHost(),
                request()->url(),
                config('app.url'),
            ],
            $string
        );
    }

    public function getTelegramSupportDeepLink(User $user)
    {
        $supportUsername = config('app.telegram_support', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'tg://resolve?domain={username}&text={text}';

        return str_replace(
            ['{username}', '{text}'],
            [$supportUsername, $supportText],
            $supportLink
        );
    }

    public function getWhatsappSupportDeepLink(User $user)
    {
        $supportPhoneNumber = config('app.whatsapp_support_phone', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'whatsapp://send/?phone={phone}&text={text}';

        return str_replace(
            ['{phone}', '{text}'],
            [urlencode($supportPhoneNumber), urlencode($supportText)],
            $supportLink
        );
    }

    public function getTelegramReferralLink(User $user)
    {
        $referralUsername = config('app.telegram_referral', '#');
        $referralText = config('app.telegram_referral_text', '#');
        $referralWebLink = 'https://t.me/{username}?text={referral_text}';
        $referralMobileLink = 'tg://resolve?domain={username}&text={referral_text}';

        // Web link by default
        if ($this->isMobileBrowser(request())) {
            $referralLink = $referralMobileLink;
        } else {
            $referralLink = $referralWebLink;
        }

        return str_replace(
            ['{username}', '{referral_text}'],
            [$referralUsername, $referralText],
            $referralLink
        );
    }

    public function getTelegramSupportLink(User $user)
    {
        $supportUsername = config('app.telegram_support', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'https://t.me/{username}?text={text}';

        return str_replace(
            ['{username}', '{text}'],
            [$supportUsername, $supportText],
            $supportLink
        );
    }

    public function getWhatsappSupportLink(User $user)
    {
        $supportPhoneNumber = config('app.whatsapp_support_phone', '#');
        $supportText = $this->getSupportText($user);
        $supportLink = 'https://api.whatsapp.com/send?phone={phone}&text={text}';

        return str_replace(
            ['{phone}', '{text}'],
            [urlencode($supportPhoneNumber), urlencode($supportText)],
            $supportLink
        );
    }


    public function getSupportText(User $user)
    {
        $text = config('app.support_text', 'Hello, I have a problem with my VPN connection. My ID: {client_id}');
        $text = str_replace('{client_id}', $this->getCurrentUserClientId($user), $text);
        return $text;
    }

    public function getCurrentUserClientId(User $user)
    {
        return str_replace('client', '', $user->email ?? $user->uuid);
    }

    /**
     * Validate UUID format.
     */
    public function isValidUuid(string $uuid): bool
    {
        return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $uuid);
    }

    /**
     * Detect if request is from a browser.
     */
    public function isBrowserUserAgent(Request $request): bool
    {
        $userAgent = $request->userAgent() ?? '';
        $userAgentLower = strtolower($userAgent);

        // НЕ-браузеры: v2ray-клиенты и технические HTTP-клиенты
        $nonBrowserKeywords = [
            // v2ray и его производные
            'xray',
            'v2ray',
            'v2raya',
            'v2raytun',
            '2rayng',
            '2rayu',
            'hiddify',
            'v3ray',
            'sing-box',
            'happ',
            // тех. клиенты
            'curl',
            'wget',
            'httpclient',
            'python-requests',
            'go-http-client',
            'okhttp',
            'postman',
            'axios',
            'node-fetch',
            'java/', // часто в UA от Java-клиентов
            'libwww',
            'perl',
        ];

        // Браузеры — определяем по наличию типичных признаков
        $browserKeywords = [
            'mozilla/',     // общий идентификатор, почти все браузеры
            'chrome/',      // Chrome и Edge
            'safari/',      // Safari
            'firefox/',     // Firefox
            'opera/',       // Opera
            'edg/',         // Microsoft Edge
            'instagram',    // WebView Instagram
            'facebook',     // Facebook in-app browser
            'telegram',     // Telegram in-app browser
            'whatsapp',     // WhatsApp WebView
            'tiktok',       // TikTok in-app browser
        ];

        // Если user-agent пустой, считаем это не браузером
        if (empty($userAgentLower)) {
            return false;
        }

        // Если содержит один из НЕ-браузерных сигнатур — это не браузер
        foreach ($nonBrowserKeywords as $keyword) {
            if (strpos($userAgentLower, $keyword) !== false) {
                return false;
            }
        }

        // Если содержит хотя бы один браузерный признак — это браузер
        foreach ($browserKeywords as $keyword) {
            if (strpos($userAgentLower, $keyword) !== false) {
                return true;
            }
        }

        // Всё остальное — тоже не браузер
        return false;
    }

    /**
     * Detect if request is from a mobile browser.
     */
    public function isMobileBrowser(Request $request): bool
    {
        $userAgent = strtolower($request->userAgent() ?? '');

        // Быстрая фильтрация: User-Agent должен содержать браузерный движок
        $isBrowserEngine = preg_match('/webkit|khtml|gecko|trident|blink/', $userAgent);

        // Должен указывать на мобильное устройство
        $isMobileDevice = preg_match('/android|iphone|ipad|ipod|iemobile|blackberry|mobile/', $userAgent);

        // Не должен быть известным приложением
        $knownAppAgents = [
            'v2ray', 'cfnetwork', 'okhttp', 'curl', 'wget', 'python', 'java', 'libhttp', 'go-http-client',
            'postman', 'axios', 'httpclient', 'nodejs', 'dart', 'electron', 'reactnative', 'flutter'
        ];

        foreach ($knownAppAgents as $appSig) {
            if (str_contains($userAgent, $appSig)) {
                return false;
            }
        }

        return $isBrowserEngine && $isMobileDevice;
    }

}
