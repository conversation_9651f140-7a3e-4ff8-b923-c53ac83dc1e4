<?php

namespace App\Services;

use App\DTOs\XuiClientDto;
use App\DTOs\XuiSettingsDto;
use App\Models\XuiServer;
use App\Models\XuiInbound;
use App\Services\HttpClientService;
use App\Services\ServerStatusService;
use Carbon\Carbon;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Cookie\CookieJar;

class XuiApiService
{
    private array $httpClients = [];

    /**
     * Get or create HTTP client for a server.
     */
    private function getHttpClient(XuiServer $server): PendingRequest
    {
        $serverKey = $server->id;

        if (!isset($this->httpClients[$serverKey])) {
            $this->httpClients[$serverKey] = $this->createHttpClient($server);
        }

        return $this->httpClients[$serverKey];
    }

    /**
     * Create HTTP client for a server.
     */
    private function createHttpClient(XuiServer $server): PendingRequest
    {
        $cookieJar = new CookieJar();

        // Add existing cookie if available
        if ($server->cookie) {
            $cookieJar = CookieJar::fromArray([
                '3x-ui' => $server->cookie,
                'lang' => 'en-EN',
            ], $server->host);
        }

        // Prepare options with cookies
        $options = ['cookies' => $cookieJar];

        // Create HTTP client with proxy support
        return HttpClientService::create($options)
            ->withHeaders([
                'X-requested-with' => 'XMLHttpRequest',
            ])
            ->withoutVerifying()
            ->timeout(30)
            ->connectTimeout(10)
            ->retry(2, 500)
            ->asForm()
            ->baseUrl($server->url);
    }

    /**
     * Authenticate with X-UI server.
     */
    public function authenticate(XuiServer $server): bool
    {
        // Check if we have a valid cookie (less than 3 hours old)
        if ($server->cookie && $server->last_login_at && $server->last_login_at->gt(now()->subHours(3))) {
            if ($this->checkAuth($server)) {
                return true;
            }
        }

        return $this->doLogin($server);
    }

    /**
     * Check if current authentication is valid.
     */
    private function checkAuth(XuiServer $server): bool
    {
        try {
            $httpClient = $this->getHttpClient($server);
            $response = $httpClient->post('/server/status');

            if ($response->successful() && $response->json('success') === true) {
                return true;
            }

            Log::info("Authentication check failed for server: {$server->name}");
            return false;
        } catch (\Exception $e) {
            Log::error("Exception during auth check: {$server->name}", [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Perform login to X-UI server.
     */
    private function doLogin(XuiServer $server): bool
    {
        try {
            $httpClient = $this->createHttpClient($server);

            $response = $httpClient->post('/login', [
                'username' => $server->username,
                'password' => $server->password,
            ]);

            if ($response->successful() &&
                $response->json('success') === true &&
                $response->cookies()->getCookieByName('3x-ui')) {

                $cookie = $response->cookies()->getCookieByName('3x-ui')->getValue();

                // Update server with new cookie
                $server->update([
                    'cookie' => $cookie,
                    'last_login_at' => now(),
                ]);

                // Update the cached HTTP client
                $this->httpClients[$server->id] = $this->createHttpClient($server);

                Log::info("Successfully authenticated with X-UI server: {$server->name}");
                return true;
            }

            Log::error("Failed to authenticate with X-UI server: {$server->name}", [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error("Exception during X-UI authentication: {$server->name}", [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get and store inbounds with clients from X-UI server.
     */
    public function syncInbounds(XuiServer $server): array
    {
        try {
            if (!$this->authenticate($server)) {
                return [];
            }

            $httpClient = $this->getHttpClient($server);
            $response = $httpClient->post('/panel/inbound/list');

            if (!$response->successful()) {
                Log::error("Failed to get inbounds from X-UI server: {$server->name}", [
                    'status' => $response->status(),
                ]);
                return [];
            }

            $data = $response->json();
            $syncedInbounds = [];

            if (isset($data['obj']) && is_array($data['obj'])) {
                foreach ($data['obj'] as $inboundData) {
                    $syncedInbound = $this->storeInbound($server, $inboundData);
                    if ($syncedInbound) {
                        $syncedInbounds[] = $syncedInbound;
                    }
                }
            }

            Log::info("Synced {count} inbounds from X-UI server: {$server->name}", [
                'count' => count($syncedInbounds),
            ]);

            return $syncedInbounds;
        } catch (\Exception $e) {
            Log::error("Exception while syncing inbounds from X-UI server: {$server->name}", [
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Store inbound data in database.
     */
    private function storeInbound(XuiServer $server, array $inboundData): ?XuiInbound
    {
        try {
            // Decode settings if it's a JSON string
            $settings = $inboundData['settings'] ?? [];
            if (is_string($settings)) {
                $settings = json_decode($settings, true) ?? [];
            }

            // Decode streamSettings if present
            $streamSettings = null;
            if (isset($inboundData['streamSettings'])) {
                if (is_string($inboundData['streamSettings'])) {
                    $streamSettings = json_decode($inboundData['streamSettings'], true);
                } else {
                    $streamSettings = $inboundData['streamSettings'];
                }
            }
            // dd($inboundData['id']);
            // Store/update inbound
            $inbound = XuiInbound::updateOrCreate(
                [
                    'xui_server_id' => $server->id,
                    'inbound_id' => $inboundData['id'],
                ],
                [
                    'up' => $inboundData['up'] ?? 0,
                    'down' => $inboundData['down'] ?? 0,
                    'total' => $inboundData['total'] ?? 0,
                    'remark' => $inboundData['remark'] ?? '',
                    'enable' => $inboundData['enable'] ?? true,
                    'expiry_time' => $inboundData['expiryTime'] ?? null,
                    'listen' => $inboundData['listen'] ?? '',
                    'port' => $inboundData['port'],
                    'protocol' => $inboundData['protocol'] ?? '',
                    'settings' => $settings,
                    'tag' => $inboundData['tag'] ?? '',
                    'sniffing' => isset($inboundData['sniffing']) ? json_decode($inboundData['sniffing'], true) : null,
                    'allocate' => isset($inboundData['allocate']) ? json_decode($inboundData['allocate'], true) : null,
                    'stream_settings' => $streamSettings,
                    'raw_data' => $inboundData,
                ]
            );

            return $inbound;
        } catch (\Exception $e) {
            Log::error("Failed to store inbound data", [
                'server' => $server->name,
                'inbound_id' => $inboundData['id'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get client statistics from inbound data.
     */
    public function getClientStats(array $inboundData): array
    {
        $clientStats = [];

        if (isset($inboundData['clientStats']) && is_array($inboundData['clientStats'])) {
            foreach ($inboundData['clientStats'] as $stat) {
                $clientStats[$stat['email']] = [
                    'id' => $stat['id'] ?? null,
                    'inbound_id' => $stat['inboundId'] ?? null,
                    'enable' => $stat['enable'] ?? true,
                    'email' => $stat['email'] ?? '',
                    'up' => $stat['up'] ?? 0,
                    'down' => $stat['down'] ?? 0,
                    'expiry_time' => $stat['expiryTime'] ?? null,
                    'total' => $stat['total'] ?? 0,
                    'reset' => $stat['reset'] ?? 0,
                ];
            }
        }

        return $clientStats;
    }

    /**
     * Get server settings from local database (fast) or X-UI API (sync).
     */
    public function getSettings(XuiServer $server, bool $forceRefresh = false): ?XuiSettingsDto
    {
        // Check if we have fresh settings (less than 1 hour old) and not forcing refresh
        if (!$forceRefresh &&
            $server->settings &&
            $server->settings_updated_at &&
            $server->settings_updated_at->gt(now()->subHour())) {

            Log::debug("Using cached settings for server: {$server->name}");
            return XuiSettingsDto::fromArray($server->settings);
        }

        // Fetch fresh settings from API
        return $this->fetchAndStoreSettings($server);
    }

    /**
     * Fetch settings from X-UI API and store in database.
     */
    public function fetchAndStoreSettings(XuiServer $server): ?XuiSettingsDto
    {
        try {
            if (!$this->authenticate($server)) {
                return null;
            }

            $httpClient = $this->getHttpClient($server);
            $response = $httpClient->post('/panel/setting/all');

            if (!$response->successful()) {
                Log::error("Failed to get settings from X-UI server: {$server->name}", [
                    'status' => $response->status(),
                ]);
                return null;
            }

            $data = $response->json();

            if (!isset($data['success']) || !$data['success']) {
                Log::error("X-UI server returned unsuccessful response: {$server->name}", [
                    'response' => $data,
                ]);
                return null;
            }

            // Store settings in database
            $server->update([
                'settings' => $data,
                'settings_updated_at' => now(),
            ]);

            Log::info("Updated settings for server: {$server->name}");
            return XuiSettingsDto::fromArray($data);
        } catch (\Exception $e) {
            Log::error("Exception while getting settings from X-UI server: {$server->name}", [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get server status from X-UI API and calculate load.
     */
    public function getServerStatus(XuiServer $server, bool $forceRefresh = false): ?array
    {
        // Check if we have fresh status (less than 5 minutes old) and not forcing refresh
        if (!$forceRefresh && $server->hasValidStatus()) {
            Log::debug("Using cached server status for server: {$server->name}");
            return $server->server_status;
        }

        // Fetch fresh status from API
        return $this->fetchAndStoreServerStatus($server);
    }

    /**
     * Fetch server status from X-UI API and store in database.
     */
    public function fetchAndStoreServerStatus(XuiServer $server): ?array
    {
        try {
            if (!$this->authenticate($server)) {
                Log::warning("Failed to authenticate for server status: {$server->name}");
                return null;
            }

            $httpClient = $this->getHttpClient($server);
            $response = $httpClient->post('/server/status');

            if (!$response->successful()) {
                Log::error("Failed to get server status from X-UI server: {$server->name}", [
                    'status' => $response->status(),
                ]);
                return null;
            }

            $data = $response->json();

            if (!isset($data['success']) || !$data['success']) {
                Log::error("X-UI server returned unsuccessful status response: {$server->name}", [
                    'response' => $data,
                ]);
                return null;
            }

            // Calculate server load using ServerStatusService
            $serverStatusService = app(ServerStatusService::class);
            $load = $serverStatusService->calculateServerLoadPercentage($data);

            // Store status and load in database
            $server->updateServerStatus($data, $load);

            Log::info("Updated server status for: {$server->name}", [
                'load' => $load,
                'cpu_cores' => $data['obj']['cpuCores'] ?? 'N/A',
                'memory_usage' => isset($data['obj']['mem']) ?
                    round(($data['obj']['mem']['current'] / $data['obj']['mem']['total']) * 100, 2) . '%' : 'N/A',
            ]);

            return $data;
        } catch (\Exception $e) {
            Log::error("Exception while getting server status from X-UI server: {$server->name}", [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Create client on X-UI server.
     */
    public function createClient(XuiServer $server, XuiInbound $inbound, $user, string $clientEmail, string $comment, bool $isDemo = false, ?Carbon $expiration = null): bool
    {
        try {
            if (!$this->authenticate($server)) {
                Log::error("Failed to authenticate for client creation: {$server->name}");
                return false;
            }

            $expirationTimestamp = 0; // 0 is never ends
            // Calculate expiration time
            if ($isDemo && $expiration) {
                $userData['demo_until'] = $expiration > 9999999999
                    ? \Carbon\Carbon::createFromTimestampMs($expiration)
                    : \Carbon\Carbon::createFromTimestamp($expiration);
            }

            if ($expiration) {
                // Use provided expiration timestamp
                $expirationTimestamp = $expiration * 1000; // X-UI expects milliseconds
            } else {
                // Default: demo: 1 hour, regular: 1 week
                $expirationTimestamp = $isDemo ? now()->addHours(1)->timestamp * 1000 : now()->addWeek()->timestamp * 1000;
            }

            // Prepare client data using User model data
            $clientData = [
                'id' => $user->uuid,
                'flow' => 'xtls-rprx-vision',
                'email' => $clientEmail,
                'limitIp' => 0,
                'totalGB' => $user->total_gb,
                'expiryTime' => $user->expiry_time ? $user->expiry_time->timestamp * 1000 : $expirationTimestamp,
                'enable' => !$user->expired && !$user->disabled_at,
                'tgId' => '',
                'subId' => "unlimited_{$user->tg_id}",
                'comment' => $user->comment,
                'reset' => 0
            ];

            $settings = json_encode(['clients' => [$clientData]]);

            $httpClient = $this->getHttpClient($server);
            $response = $httpClient->post('/panel/inbound/addClient', [
                'id' => $inbound->inbound_id,
                'settings' => $settings
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['success']) && $data['success']) {
                    Log::info("Successfully created client on X-UI server", [
                        'server' => $server->name,
                        'inbound_id' => $inbound->inbound_id,
                        'user_uuid' => $user->uuid,
                        'client_email' => $clientEmail,
                        'is_demo' => $isDemo
                    ]);
                    return true;
                } else {
                    Log::error("X-UI server returned unsuccessful response for client creation", [
                        'server' => $server->name,
                        'inbound_id' => $inbound->inbound_id,
                        'response' => $data
                    ]);
                }
            } else {
                Log::error("Failed to create client on X-UI server", [
                    'server' => $server->name,
                    'inbound_id' => $inbound->inbound_id,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
            }

            return false;

        } catch (\Exception $e) {
            Log::error("Exception while creating client on X-UI server", [
                'server' => $server->name,
                'inbound_id' => $inbound->inbound_id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

}
