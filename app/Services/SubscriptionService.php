<?php

namespace App\Services;

use App\DTOs\SubscriptionDto;
use App\Models\User;
use App\Models\XuiServer;
use App\Models\XuiClient;
use App\Models\XuiInbound;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    public function __construct(
        private XuiApiService $xuiApiService
    ) {}

    /**
     * Get subscription content for a user by UUID.
     */
    public function getSubscriptionByUuid(string $uuid): ?SubscriptionDto
    {
        $user = User::findByUuid($uuid);
        if (!$user) {
            Log::warning("User not found for UUID: {$uuid}");
            return null;
        }

        // Get all active clients for this user from local database
        $clients = $user->xuiClients()
            ->whereHas('xuiServer', function ($query) {
                $query->where('is_active', true);
            })
            ->where('enable', true)
            ->get();

        if ($clients->isEmpty()) {
            Log::info("No active clients found for user: {$uuid}");
            return null;
        }

        $vlessLinks = [];

        foreach ($clients as $client) {
            try {
                // Check if client is not expired
                if (!$client->isActive()) {
                    Log::debug("Skipping inactive client: {$client->client_id}");
                    continue;
                }

                // Generate vless URL from stored inbound data
                $vlessUrl = $client->generateVlessUrl();

                if ($vlessUrl) {
                    $vlessLinks[] = $vlessUrl;
                }
            } catch (\Exception $e) {
                Log::error("Error generating vless URL for client {$client->client_id}", [
                    'server' => $client->xuiServer->name,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if (empty($vlessLinks)) {
            Log::info("No valid subscription content found for user: {$uuid}");
            return null;
        }

        $content = implode("\n", $vlessLinks);

        // Get traffic data from User model (centralized source)
        return new SubscriptionDto(
            content: $content,
            upload: $user->up_traffic,
            download: $user->down_traffic,
            total: $user->total_gb ?: 0 // 0 means unlimited
        );
    }



    /**
     * Update or create client record.
     */
    private function updateClientRecord(User $user, XuiServer $server, $inbound, array $clientData, ?array $trafficStats = null): void
    {
        try {
            XuiClient::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'xui_server_id' => $server->id,
                    'xui_inbound_id' => $inbound->id,
                    'client_id' => $clientData['id'],
                ],
                [
                    'sub_id' => $clientData['subId'] ?? null,
                    'email' => $clientData['email'] ?? null,
                    'enable' => $clientData['enable'] ?? true,
                    'disabled_at' => ($clientData['enable'] ?? true) ? null : now(),
                    'tg_id' => $clientData['tgId'] ?? null,
                    'flow' => $clientData['flow'] ?? null,
                    'limit_ip' => $clientData['limitIp'] ?? 0,
                    'total_gb' => $clientData['totalGB'] ?? 0,
                    'up_traffic' => $trafficStats['up'] ?? 0,
                    'down_traffic' => $trafficStats['down'] ?? 0,
                    'expiry_time' => isset($clientData['expiryTime']) && $clientData['expiryTime'] ?
                        now()->createFromTimestamp($clientData['expiryTime'] / 1000) : null,
                    'expired' => isset($clientData['expiryTime']) && $clientData['expiryTime'] &&
                        now()->createFromTimestamp($clientData['expiryTime'] / 1000)->isPast(),
                    'reset' => $clientData['reset'] ?? 0,
                    'comment' => $clientData['comment'] ?? null,
                    'raw_data' => $clientData,
                ]
            );
        } catch (\Exception $e) {
            Log::error("Failed to update client record", [
                'user_id' => $user->id,
                'server_id' => $server->id,
                'inbound_id' => $inbound->id,
                'client_id' => $clientData['id'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Update traffic statistics for clients from a specific inbound.
     */
    public function updateTrafficStats(XuiInbound $inbound, array $inboundData): void
    {
        try {
            $clientStats = $this->xuiApiService->getClientStats($inboundData);

            foreach ($clientStats as $email => $stats) {
                // Find client by email and inbound
                $client = XuiClient::where('xui_inbound_id', $inbound->id)
                    ->where('email', $email)
                    ->first();

                if ($client) {
                    $client->update([
                        'up_traffic' => $stats['up'],
                        'down_traffic' => $stats['down'],
                    ]);

                    Log::debug("Updated traffic stats for client: {$email}", [
                        'up' => $stats['up'],
                        'down' => $stats['down'],
                        'inbound' => $inbound->remark,
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error("Failed to update traffic stats for inbound: {$inbound->remark}", [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Sync all subscriptions from all servers.
     */
    public function syncAllSubscriptions(): void
    {
        $servers = XuiServer::active()->get();

        foreach ($servers as $server) {
            $this->syncServerSubscriptions($server);
        }
    }

    /**
     * Sync subscriptions from a specific server.
     */
    public function syncServerSubscriptions(XuiServer $server): void
    {
        try {
            Log::info("Starting sync for server: {$server->name}");

            // First, fetch and store server settings
            $this->xuiApiService->fetchAndStoreSettings($server);

            // Sync inbounds and clients
            $inbounds = $this->xuiApiService->syncInbounds($server);
            $processedCount = 0;

            foreach ($inbounds as $inbound) {
                // dd($inbound->id);
                // Get clients from inbound settings
                $clientsData = $inbound->getClientsFromSettings();

                // Get client statistics from raw_data
                $clientStats = $this->xuiApiService->getClientStats($inbound->raw_data);

                foreach ($clientsData as $clientData) {
                    $clientData['tgId'] = empty($clientData['tgId']) ? null : $clientData['tgId'];
                    // First try to find existing XuiClient record
                    $existingClient = XuiClient::where('xui_server_id', $server->id)
                        ->where('xui_inbound_id', $inbound->id)
                        ->where('client_id', $clientData['id'])
                        ->first();

                    $user = null;
                    if ($existingClient) {
                        // Use existing user from XuiClient
                        $user = $existingClient->user;
                    } else {
                        // Find or create user based on client_id, tg_id or email
                        $user = User::findOrCreateByClientIdOrTgIdOrEmail(
                            $clientData['id'],
                            $clientData['tgId'] ?? null,
                            $clientData['email'] ?? null
                        );

                        // Updating user's expiry time and status from client data
                        $this->updateUserExpiration($user, $clientData);
                    }

                    if ($user) {
                        // Update user tg_id if provided and different
                        if ($clientData['tgId'] && $user->tg_id !== $clientData['tgId']) {
                            $user->update(['tg_id' => $clientData['tgId']]);
                        } elseif (!$user->tg_id && preg_match('/client(\d+)/', $clientData['email'], $matches)) {
                            $user->update(['tg_id' => $matches[1]]);
                        }

                        // Updating user's expiry time and status from client data
                        $this->updateUserExpiration($user, $clientData);

                        // Get traffic stats for this client
                        $trafficStats = $clientStats[$clientData['email']] ?? null;

                        $this->updateClientRecord($user, $server, $inbound, $clientData, $trafficStats);
                        $this->updateUserTrafficStats($user);
                        $processedCount++;
                    } else {
                        Log::warning("Could not find or create user for client", [
                            'client_id' => $clientData['id'] ?? 'unknown',
                            'email' => $clientData['email'] ?? null,
                            'tg_id' => $clientData['tgId'] ?? null,
                            'server' => $server->name,
                        ]);
                    }
                }

                // Also update traffic stats for existing clients that might not be in settings
                $this->updateTrafficStats($inbound, $inbound->raw_data);
            }

            // Update last sync time
            $server->update(['last_sync_at' => now()]);

            Log::info("Completed sync for server: {$server->name}", [
                'total_inbounds' => count($inbounds),
                'processed_clients' => $processedCount,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to sync server: {$server->name}", [
                'error' => $e->getMessage(),
            ]);
        }
    }

    private function updateUserExpiration(User $user, array $clientData): void
    {
        $clientExpiryTime = isset($clientData['expiryTime']) && $clientData['expiryTime'] > 0 ?
            now()->createFromTimestampMs($clientData['expiryTime']) : null;

        $user->update([
            'expiry_time' => $clientExpiryTime,
            'expired' => $clientExpiryTime && $clientExpiryTime->isPast(),
        ]);
    }

    /**
     * Update status of expired or disabled clients without deleting them.
     */
    public function cleanupExpiredClients(): void
    {
        try {
            // Mark clients as disabled and set disabled_at timestamp
            $disabledCount = XuiClient::where('enable', false)
                ->whereNull('disabled_at')
                ->update(['disabled_at' => now()]);

            // Mark clients as expired if their expiry_time has passed
            $expiredCount = XuiClient::where('expiry_time', '<', now())
                ->where('expired', false)
                ->update(['expired' => true]);

            // Mark clients as expired if they exceeded traffic limits
            $trafficExceededCount = XuiClient::where('expired', false)
                ->where('total_gb', '>', 0)
                ->whereRaw('(up_traffic + down_traffic) >= total_gb')
                ->update(['expired' => true]);

            Log::info("Updated client statuses", [
                'disabled_marked' => $disabledCount,
                'expired_marked' => $expiredCount,
                'traffic_exceeded_marked' => $trafficExceededCount,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to cleanup expired clients", [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get subscription statistics for a user.
     */
    public function getUserStats(string $uuid): ?array
    {
        $user = User::findByUuid($uuid);
        if (!$user) {
            return null;
        }

        $clients = $user->xuiClients()
            ->whereHas('xuiServer', function ($query) {
                $query->where('is_active', true);
            })
            ->get();

        // Get traffic data from User model (centralized source)
        $totalTraffic = $user->total_gb;
        $usedTraffic = $user->up_traffic + $user->down_traffic;
        $activeClients = $clients->where('enable', true)->whereNull('disabled_at')->where('expired', false)->count();
        $disabledClients = $clients->where('enable', false)->orWhereNotNull('disabled_at')->count();
        $expiredClients = $clients->where('expired', true)->count();

        return [
            'user_id' => $user->id,
            'uuid' => $user->uuid,
            'tg_id' => $user->tg_id,
            'email' => $user->email,
            'comment' => $user->comment,
            'expiry_time' => $user->expiry_time?->toISOString(),
            'expired' => $user->expired,
            'disabled_at' => $user->disabled_at?->toISOString(),
            'total_clients' => $clients->count(),
            'active_clients' => $activeClients,
            'disabled_clients' => $disabledClients,
            'expired_clients' => $expiredClients,
            'total_traffic_bytes' => $totalTraffic,
            'used_traffic_bytes' => $usedTraffic,
            'up_traffic_bytes' => $user->up_traffic,
            'down_traffic_bytes' => $user->down_traffic,
            'total_traffic_gb' => round($totalTraffic / 1024 / 1024 / 1024, 2),
            'used_traffic_gb' => round($usedTraffic / 1024 / 1024 / 1024, 2),
            'up_traffic_gb' => round($user->up_traffic / 1024 / 1024 / 1024, 2),
            'down_traffic_gb' => round($user->down_traffic / 1024 / 1024 / 1024, 2),
            'usage_percentage' => $totalTraffic > 0 ? round(($usedTraffic / $totalTraffic) * 100, 2) : 0,
        ];
    }

    /**
     * Update user traffic statistics by summing all clients' traffic.
     */
    private function updateUserTrafficStats(User $user): void
    {
        try {
            // Sum traffic from all user's clients
            $trafficStats = $user->xuiClients()
                ->selectRaw('
                    SUM(up_traffic) as total_up_traffic,
                    SUM(down_traffic) as total_down_traffic,
                    SUM(up_traffic + down_traffic) as total_used_gb,
                    MAX(total_gb) as total_gb
                ')
                ->first();

            // Update user with aggregated traffic stats
            $user->update([
                'up_traffic' => $trafficStats->total_up_traffic ?? 0,
                'down_traffic' => $trafficStats->total_down_traffic ?? 0,
                'used_gb' => $trafficStats->total_used_gb ?? 0,
                'total_gb' => $trafficStats->total_gb ?? 0,
            ]);

            Log::debug("Updated user traffic stats", [
                'user_id' => $user->id,
                'up_traffic' => $trafficStats->total_up_traffic ?? 0,
                'down_traffic' => $trafficStats->total_down_traffic ?? 0,
                'used_gb' => $trafficStats->total_used_gb ?? 0,
                'total_gb' => $trafficStats->total_gb ?? 0,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update user traffic stats", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
