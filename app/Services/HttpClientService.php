<?php

namespace App\Services;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HttpClientService
{
    /**
     * Create HTTP client with proxy support.
     */
    public static function create(array $options = []): PendingRequest
    {
        // Add SOCKS5 proxy if configured
        $socks5Proxy = config('app.socks5_proxy');
        if ($socks5Proxy) {
            $options['proxy'] = [
                'http' => $socks5Proxy,
                'https' => $socks5Proxy,
            ];
            Log::debug("Using SOCKS5 proxy for HTTP client: {$socks5Proxy}");
        }

        return Http::withOptions($options);
    }

    /**
     * Create HTTP client with proxy and common options.
     */
    public static function createWithDefaults(array $additionalOptions = []): PendingRequest
    {
        $defaultOptions = [
            'timeout' => 30,
            'connect_timeout' => 10,
            'verify' => false,
        ];

        $options = array_merge($defaultOptions, $additionalOptions);

        return self::create($options);
    }

    /**
     * Create HTTP client for API calls with proxy support.
     */
    public static function createForApi(array $headers = [], array $options = []): PendingRequest
    {
        $defaultHeaders = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'User-Agent' => 'Laravel-App/1.0',
        ];

        $headers = array_merge($defaultHeaders, $headers);

        return self::createWithDefaults($options)
            ->withHeaders($headers)
            ->retry(2, 500);
    }

    /**
     * Check if proxy is configured.
     */
    public static function isProxyConfigured(): bool
    {
        return !empty(config('app.socks5_proxy'));
    }

    /**
     * Get proxy configuration.
     */
    public static function getProxyConfig(): ?string
    {
        return config('app.socks5_proxy');
    }
}
