<?php

use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SupportController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Test QR code page
Route::get('/test-qr', function () {
    return view('test-qr');
});

// Test accordion page
Route::get('/test-accordion', function () {
    return view('test-accordion');
});

// Subscription endpoints with middleware
Route::middleware(['App\Http\Middleware\SubscriptionMiddleware'])->group(function () {
    // Main subscription endpoint
    Route::get('/subs/{uuid}', [SubscriptionController::class, 'show'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Modern subscription endpoint
    Route::get('/subs/{uuid}/modern', [SubscriptionController::class, 'showModern'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Modern subscription endpoint without QR
    Route::get('/subs/{uuid}/modern-no-qr', [SubscriptionController::class, 'showModernNoQR'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // User statistics endpoint (for debugging/monitoring)
    Route::get('/subs/{uuid}/stats', [SubscriptionController::class, 'stats'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Force refresh subscription content
    Route::post('/subs/{uuid}/refresh', [SubscriptionController::class, 'refresh'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');
});

// Support routes
Route::get('/support/{uuid}', [SupportController::class, 'index'])
    ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}')
    ->name('support.index');

Route::post('/support/rating', [SupportController::class, 'submitRating'])->name('support.rating');
Route::get('/support/stats', [SupportController::class, 'getRatingStats'])->name('support.stats');
