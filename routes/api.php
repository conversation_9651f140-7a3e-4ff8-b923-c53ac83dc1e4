<?php

use App\Http\Controllers\Api\SubscriptionApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Subscription API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('subscription')->group(function () {
    // Get API information
    Route::get('/info', [SubscriptionApiController::class, 'info']);

    // Get subscription link by user identifier
    Route::any('/link', [SubscriptionApiController::class, 'getSubscriptionLink']);
});

/*
|--------------------------------------------------------------------------
| User API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('user')->group(function () {
    // Create new user with XUI clients
    Route::post('/create', [SubscriptionApiController::class, 'createUser']);
});
