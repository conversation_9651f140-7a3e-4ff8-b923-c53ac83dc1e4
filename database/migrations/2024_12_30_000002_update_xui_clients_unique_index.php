<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('xui_clients', function (Blueprint $table) {
            // Drop old unique constraint
            $table->dropUnique(['user_id', 'xui_server_id', 'client_id']);
            
            // Add new unique constraint that includes xui_inbound_id
            $table->unique(['user_id', 'xui_server_id', 'xui_inbound_id', 'client_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('xui_clients', function (Blueprint $table) {
            // Drop new unique constraint
            $table->dropUnique(['user_id', 'xui_server_id', 'xui_inbound_id', 'client_id']);
            
            // Restore old unique constraint
            $table->unique(['user_id', 'xui_server_id', 'client_id']);
        });
    }
};
