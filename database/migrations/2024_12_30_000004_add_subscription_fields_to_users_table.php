<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->text('comment')->nullable()->after('demo_until');
            $table->timestamp('expiry_time')->nullable()->after('comment');
            $table->boolean('expired')->default(false)->after('expiry_time');
            $table->timestamp('disabled_at')->nullable()->after('expired');
            $table->bigInteger('total_gb')->default(0)->after('disabled_at')->comment('Total traffic limit in bytes');
            $table->bigInteger('used_gb')->default(0)->after('total_gb')->comment('Used traffic in bytes');
            $table->bigInteger('up_traffic')->default(0)->after('used_gb')->comment('Upload traffic in bytes');
            $table->bigInteger('down_traffic')->default(0)->after('up_traffic')->comment('Download traffic in bytes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'comment',
                'expiry_time',
                'expired',
                'disabled_at',
                'total_gb',
                'used_gb',
                'up_traffic',
                'down_traffic'
            ]);
        });
    }
};
