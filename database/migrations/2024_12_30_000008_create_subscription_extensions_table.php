<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_extensions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('old_expiry_time')->nullable()->comment('Previous expiry time');
            $table->timestamp('new_expiry_time')->nullable()->comment('New expiry time');
            $table->string('source')->nullable()->comment('Source of extension request (telegram_bot, api, admin, etc.)');
            $table->text('comment')->nullable()->comment('Optional comment for extension');
            $table->string('request_ip', 45)->nullable()->comment('IP address of request');
            $table->text('request_user_agent')->nullable()->comment('User agent of request');
            $table->json('additional_data')->nullable()->comment('Additional data in JSON format');
            $table->timestamps();

            // Indexes for efficient queries
            $table->index(['user_id', 'created_at']);
            $table->index('source');
            $table->index('created_at');
            $table->index(['user_id', 'source']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_extensions');
    }
};
