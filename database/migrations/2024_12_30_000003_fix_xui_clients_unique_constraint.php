<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if old unique constraint exists and drop it
        $indexes = DB::select("SHOW INDEX FROM xui_clients WHERE Key_name = 'xui_clients_user_id_xui_server_id_client_id_unique'");
        if (!empty($indexes)) {
            DB::statement('ALTER TABLE xui_clients DROP INDEX xui_clients_user_id_xui_server_id_client_id_unique');
        }

        // Check if new unique constraint doesn't exist and add it
        $newIndexes = DB::select("SHOW INDEX FROM xui_clients WHERE Key_name = 'xui_clients_unique_idx'");
        if (empty($newIndexes)) {
            DB::statement('ALTER TABLE xui_clients ADD UNIQUE KEY xui_clients_unique_idx (user_id, xui_server_id, xui_inbound_id, client_id)');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if new unique constraint exists and drop it
        $newIndexes = DB::select("SHOW INDEX FROM xui_clients WHERE Key_name = 'xui_clients_unique_idx'");
        if (!empty($newIndexes)) {
            DB::statement('ALTER TABLE xui_clients DROP INDEX xui_clients_unique_idx');
        }

        // Check if old unique constraint doesn't exist and add it
        $indexes = DB::select("SHOW INDEX FROM xui_clients WHERE Key_name = 'xui_clients_user_id_xui_server_id_client_id_unique'");
        if (empty($indexes)) {
            DB::statement('ALTER TABLE xui_clients ADD UNIQUE KEY xui_clients_user_id_xui_server_id_client_id_unique (user_id, xui_server_id, client_id)');
        }
    }
};
