<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Удаляем старый обычный индекс, если он существует
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['tg_id']); // удаление обычного индекса
        });

        // Добавляем уникальный индекс
        Schema::table('users', function (Blueprint $table) {
            $table->unique('tg_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Удаляем уникальный индекс
        Schema::table('users', function (Blueprint $table) {
            $table->dropUnique(['tg_id']);
        });

        // Восстанавливаем обычный индекс, если нужно
        Schema::table('users', function (Blueprint $table) {
            $table->index('tg_id');
        });
    }
};
