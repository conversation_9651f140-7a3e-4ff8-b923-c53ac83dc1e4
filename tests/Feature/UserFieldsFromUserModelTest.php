<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\XuiServer;
use App\Models\XuiInbound;
use App\Models\XuiClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class UserFieldsFromUserModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test server and inbound
        $server = XuiServer::create([
            'name' => 'Test Server',
            'host' => 'test.example.com',
            'port' => 443,
            'web_base_path' => '/test',
            'username' => 'admin',
            'password' => 'password',
            'is_active' => true,
        ]);

        XuiInbound::create([
            'xui_server_id' => $server->id,
            'inbound_id' => 1,
            'remark' => 'Test Inbound',
            'port' => 443,
            'protocol' => 'vless',
            'settings' => ['clients' => []],
            'tag' => 'test-inbound',
            'enable' => true,
        ]);
    }

    /** @test */
    public function test_expiry_time_logic_with_expiration_parameter()
    {
        // Test 1: expiration provided (ignore demo flag)
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '100001',
            'comment' => 'Test with expiration',
            'demo' => true, // Should be ignored
            'expiration' => 1735689600 // 2024-01-01 00:00:00
        ]);

        $response->assertStatus(200);
        
        $user = User::where('tg_id', '100001')->first();
        $this->assertNotNull($user);
        $this->assertEquals('2024-01-01 00:00:00', $user->expiry_time->format('Y-m-d H:i:s'));
    }

    /** @test */
    public function test_expiry_time_logic_with_demo_flag()
    {
        // Test 2: demo flag without expiration (1 day)
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '100002',
            'comment' => 'Demo user',
            'demo' => true
        ]);

        $response->assertStatus(200);
        
        $user = User::where('tg_id', '100002')->first();
        $this->assertNotNull($user);
        
        // Should be approximately 1 day from now
        $expectedTime = now()->addDay();
        $this->assertTrue($user->expiry_time->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function test_expiry_time_logic_without_demo_flag()
    {
        // Test 3: no demo flag, no expiration (1 week)
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '100003',
            'comment' => 'Regular user'
        ]);

        $response->assertStatus(200);
        
        $user = User::where('tg_id', '100003')->first();
        $this->assertNotNull($user);
        
        // Should be approximately 1 week from now
        $expectedTime = now()->addWeek();
        $this->assertTrue($user->expiry_time->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function test_expiration_in_milliseconds()
    {
        // Test 4: expiration in milliseconds
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '100004',
            'comment' => 'Test with milliseconds',
            'expiration' => 1735689600000 // 2024-01-01 00:00:00 in milliseconds
        ]);

        $response->assertStatus(200);
        
        $user = User::where('tg_id', '100004')->first();
        $this->assertNotNull($user);
        $this->assertEquals('2024-01-01 00:00:00', $user->expiry_time->format('Y-m-d H:i:s'));
    }

    /** @test */
    public function test_xui_client_fields_from_user_model()
    {
        // Create user with specific data
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '100005',
            'comment' => 'Test user fields',
            'demo' => true,
            'expiration' => 1735689600
        ]);

        $response->assertStatus(200);
        
        $user = User::where('tg_id', '100005')->first();
        $xuiClient = XuiClient::where('user_id', $user->id)->first();
        
        $this->assertNotNull($xuiClient);
        
        // Check that XuiClient fields match User fields
        $this->assertEquals($user->tg_id, $xuiClient->tg_id);
        $this->assertEquals($user->comment, $xuiClient->comment);
        $this->assertEquals($user->expiry_time->format('Y-m-d H:i:s'), $xuiClient->expiry_time->format('Y-m-d H:i:s'));
        $this->assertEquals($user->expired, $xuiClient->expired);
        $this->assertEquals($user->disabled_at, $xuiClient->disabled_at);
        $this->assertEquals($user->total_gb, $xuiClient->total_gb);
        $this->assertEquals($user->used_gb, $xuiClient->used_gb);
        $this->assertEquals($user->up_traffic, $xuiClient->up_traffic);
        $this->assertEquals($user->down_traffic, $xuiClient->down_traffic);
    }

    /** @test */
    public function test_user_fields_initialization()
    {
        // Test demo user initialization
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '100006',
            'comment' => 'Demo initialization test',
            'demo' => true
        ]);

        $response->assertStatus(200);
        
        $user = User::where('tg_id', '100006')->first();
        
        // Check demo user fields
        $this->assertEquals('Demo initialization test', $user->comment);
        $this->assertEquals(false, $user->expired);
        $this->assertNull($user->disabled_at);
        $this->assertEquals(1073741824, $user->total_gb); // 1GB for demo
        $this->assertEquals(0, $user->used_gb);
        $this->assertEquals(0, $user->up_traffic);
        $this->assertEquals(0, $user->down_traffic);
        
        // Test regular user initialization
        $response2 = $this->postJson('/api/user/create', [
            'tg_id' => '100007',
            'comment' => 'Regular initialization test',
            'demo' => false
        ]);

        $response2->assertStatus(200);
        
        $user2 = User::where('tg_id', '100007')->first();
        
        // Check regular user fields
        $this->assertEquals('Regular initialization test', $user2->comment);
        $this->assertEquals(false, $user2->expired);
        $this->assertNull($user2->disabled_at);
        $this->assertEquals(0, $user2->total_gb); // Unlimited for regular
        $this->assertEquals(0, $user2->used_gb);
        $this->assertEquals(0, $user2->up_traffic);
        $this->assertEquals(0, $user2->down_traffic);
    }

    /** @test */
    public function test_unique_email_generation()
    {
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '100008',
            'comment' => 'Email test'
        ]);

        $response->assertStatus(200);
        
        $user = User::where('tg_id', '100008')->first();
        $xuiClient = XuiClient::where('user_id', $user->id)->first();
        
        // Check that email includes inbound_id
        $this->assertStringContains('_1', $xuiClient->email); // Should be client100008_1
        $this->assertEquals('client100008_1', $xuiClient->email);
    }
}
