<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\XuiServer;
use App\Models\XuiInbound;
use App\Models\XuiClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class UserStatsApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test server and inbound
        $server = XuiServer::create([
            'name' => 'Test Server',
            'host' => 'test.example.com',
            'port' => 443,
            'web_base_path' => '/test',
            'username' => 'admin',
            'password' => 'password',
            'is_active' => true,
        ]);

        XuiInbound::create([
            'xui_server_id' => $server->id,
            'inbound_id' => 1,
            'remark' => 'Test Inbound',
            'port' => 443,
            'protocol' => 'vless',
            'settings' => ['clients' => []],
            'tag' => 'test-inbound',
            'enable' => true,
        ]);
    }

    /** @test */
    public function test_get_user_stats_by_tg_id()
    {
        // Create test user
        $user = User::create([
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'tg_id' => '100001',
            'email' => 'client100001',
            'comment' => 'Test user',
            'expiry_time' => now()->addDays(30),
            'expired' => false,
            'disabled_at' => null,
            'total_gb' => 1073741824, // 1GB
            'used_gb' => 536870912,   // 512MB
            'up_traffic' => 268435456, // 256MB
            'down_traffic' => 268435456, // 256MB
        ]);

        $response = $this->getJson('/api/user/stats?tg_id=100001');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'tg_id' => '100001',
                         'uuid' => '550e8400-e29b-41d4-a716-************',
                         'email' => 'client100001',
                         'comment' => 'Test user',
                         'up' => 268435456,
                         'down' => 268435456,
                         'total' => 1073741824,
                         'used_traffic' => 536870912,
                         'enable' => true,
                         'is_expired' => false,
                         'started_using' => true,
                         'status' => 'active',
                     ]
                 ]);
    }

    /** @test */
    public function test_get_user_stats_by_uuid()
    {
        $user = User::create([
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'tg_id' => '100002',
            'email' => 'client100002',
            'comment' => 'UUID test',
            'expiry_time' => now()->addDays(1),
            'expired' => false,
            'total_gb' => 0, // Unlimited
            'up_traffic' => 1048576, // 1MB
            'down_traffic' => 2097152, // 2MB
        ]);

        $response = $this->getJson('/api/user/stats?uuid=550e8400-e29b-41d4-a716-************');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'uuid' => '550e8400-e29b-41d4-a716-************',
                         'total' => 0, // Unlimited
                         'remaining_traffic' => null,
                         'usage_percentage' => 0,
                     ]
                 ]);
    }

    /** @test */
    public function test_get_user_stats_by_email()
    {
        $user = User::create([
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'tg_id' => '100003',
            'email' => 'client100003',
            'demo_until' => now()->addDay(),
            'expiry_time' => now()->addDay(),
            'total_gb' => 1073741824,
            'up_traffic' => 0,
            'down_traffic' => 0,
        ]);

        $response = $this->getJson('/api/user/stats?email=client100003');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'email' => 'client100003',
                         'is_demo' => true,
                         'status' => 'demo',
                         'started_using' => false,
                     ]
                 ]);
    }

    /** @test */
    public function test_expired_user_stats()
    {
        $user = User::create([
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'tg_id' => '100004',
            'email' => 'client100004',
            'expiry_time' => now()->subDays(5), // Expired 5 days ago
            'expired' => true,
            'total_gb' => 1073741824,
            'up_traffic' => 536870912,
            'down_traffic' => 536870912,
        ]);

        $response = $this->getJson('/api/user/stats?tg_id=100004');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'enable' => false,
                         'is_expired' => true,
                         'status' => 'expired',
                         'days_until_expiry' => 0,
                         'needs_renewal' => true,
                     ]
                 ]);
    }

    /** @test */
    public function test_disabled_user_stats()
    {
        $user = User::create([
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'tg_id' => '100005',
            'email' => 'client100005',
            'disabled_at' => now()->subHour(),
            'expiry_time' => now()->addDays(30),
            'total_gb' => 1073741824,
        ]);

        $response = $this->getJson('/api/user/stats?tg_id=100005');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'enable' => false,
                         'status' => 'disabled',
                         'can_connect' => false,
                     ]
                 ]);
    }

    /** @test */
    public function test_user_not_found()
    {
        $response = $this->getJson('/api/user/stats?tg_id=nonexistent');

        $response->assertStatus(404)
                 ->assertJson([
                     'success' => false,
                     'message' => 'User not found',
                 ]);
    }

    /** @test */
    public function test_missing_identifier()
    {
        $response = $this->getJson('/api/user/stats');

        $response->assertStatus(422)
                 ->assertJson([
                     'success' => false,
                     'message' => 'At least one of tg_id, uuid, or email is required',
                 ]);
    }

    /** @test */
    public function test_user_with_clients_stats()
    {
        $user = User::create([
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'tg_id' => '100006',
            'email' => 'client100006',
            'expiry_time' => now()->addDays(30),
            'total_gb' => 1073741824,
            'up_traffic' => 268435456,
            'down_traffic' => 268435456,
        ]);

        // Create clients for the user
        $server = XuiServer::first();
        $inbound = XuiInbound::first();

        XuiClient::create([
            'user_id' => $user->id,
            'xui_server_id' => $server->id,
            'xui_inbound_id' => $inbound->id,
            'client_id' => $user->uuid,
            'email' => 'client100006_1',
            'enable' => true,
            'tg_id' => $user->tg_id,
            'sub_id' => "unlimited_{$user->tg_id}",
            'comment' => $user->comment,
        ]);

        $response = $this->getJson('/api/user/stats?tg_id=100006');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'servers' => [
                             'Test Server' => [1]
                         ],
                         'total_clients' => 1,
                         'active_clients' => 1,
                         'disabled_clients' => 0,
                         'expired_clients' => 0,
                         'can_connect' => true,
                     ]
                 ]);
    }

    /** @test */
    public function test_traffic_calculations()
    {
        $user = User::create([
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'tg_id' => '100007',
            'email' => 'client100007',
            'expiry_time' => now()->addDays(30),
            'total_gb' => 1073741824, // 1GB
            'up_traffic' => 536870912,   // 512MB
            'down_traffic' => 268435456, // 256MB
            'created_at' => now()->subDays(10),
        ]);

        $response = $this->getJson('/api/user/stats?tg_id=100007');

        $data = $response->json('data');

        $this->assertEquals(805306368, $data['used_traffic']); // 512MB + 256MB
        $this->assertEquals(268435456, $data['remaining_traffic']); // 1GB - 768MB
        $this->assertEquals(75.0, $data['usage_percentage']); // 768MB / 1GB * 100
        $this->assertEquals(10, $data['days_since_creation']);
        $this->assertGreaterThan(0, $data['daily_average']);
    }
}
