<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\XuiServer;
use App\Models\XuiInbound;
use App\Models\XuiClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SubscriptionApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Set app URL for testing
        config(['app.url' => 'https://test-domain.com']);
    }

    /** @test */
    public function it_returns_api_info()
    {
        $response = $this->getJson('/api/subscription/info');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'api_version' => '1.0',
                    'endpoints' => [
                        'subscription_link' => [
                            'method' => 'POST',
                            'url' => '/api/subscription/link',
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_returns_subscription_link_by_tg_id()
    {
        $user = User::factory()->create([
            'tg_id' => '123456789',
            'email' => '<EMAIL>'
        ]);

        $response = $this->postJson('/api/subscription/link', [
            'tg_id' => '123456789'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'subscription_link' => "https://test-domain.com/subs/{$user->uuid}/modern",
                    'uuid' => $user->uuid,
                    'user' => [
                        'id' => $user->id,
                        'email' => $user->email,
                        'tg_id' => $user->tg_id,
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_returns_subscription_link_by_email()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $response = $this->postJson('/api/subscription/link', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'subscription_link' => "https://test-domain.com/subs/{$user->uuid}/modern",
                    'uuid' => $user->uuid,
                ]
            ]);
    }

    /** @test */
    public function it_returns_subscription_link_by_client_id()
    {
        $user = User::factory()->create([
            'email' => 'client12345'
        ]);

        $response = $this->postJson('/api/subscription/link', [
            'client_id' => '12345'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'subscription_link' => "https://test-domain.com/subs/{$user->uuid}/modern",
                    'uuid' => $user->uuid,
                ]
            ]);
    }

    /** @test */
    public function it_returns_error_when_no_identifier_provided()
    {
        $response = $this->postJson('/api/subscription/link', []);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'At least one identifier (tg_id, email, or client_id) must be provided'
            ]);
    }

    /** @test */
    public function it_returns_error_when_user_not_found()
    {
        $response = $this->postJson('/api/subscription/link', [
            'tg_id' => 'nonexistent'
        ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'User not found'
            ]);
    }

    /** @test */
    public function it_validates_email_format()
    {
        $response = $this->postJson('/api/subscription/link', [
            'email' => 'invalid-email'
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => [
                    'email' => ['The email field must be a valid email address.']
                ]
            ]);
    }

    /** @test */
    public function it_prioritizes_tg_id_over_other_identifiers()
    {
        $userByTgId = User::factory()->create([
            'tg_id' => '123456789',
            'email' => '<EMAIL>'
        ]);

        $userByEmail = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $response = $this->postJson('/api/subscription/link', [
            'tg_id' => '123456789',
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'uuid' => $userByTgId->uuid,
                    'user' => [
                        'email' => '<EMAIL>'
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_prioritizes_email_over_client_id()
    {
        $userByEmail = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $userByClientId = User::factory()->create([
            'email' => 'client12345'
        ]);

        $response = $this->postJson('/api/subscription/link', [
            'email' => '<EMAIL>',
            'client_id' => '12345'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'uuid' => $userByEmail->uuid,
                    'user' => [
                        'email' => '<EMAIL>'
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_handles_multiple_identifiers_correctly()
    {
        $user = User::factory()->create([
            'tg_id' => '123456789',
            'email' => 'client12345'
        ]);

        // Should find by tg_id (highest priority)
        $response = $this->postJson('/api/subscription/link', [
            'tg_id' => '123456789',
            'email' => 'client12345',
            'client_id' => '12345'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'uuid' => $user->uuid,
                ]
            ]);
    }

    /** @test */
    public function it_creates_new_user_successfully()
    {
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '123456789',
            'comment' => 'Test user comment',
            'demo' => false
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'user' => [
                        'tg_id' => '123456789',
                        'email' => 'client123456789',
                        'demo_until' => null,
                    ],
                    'is_demo' => false,
                    'expiration' => null
                ]
            ]);

        // Check user was created in database
        $this->assertDatabaseHas('users', [
            'tg_id' => '123456789',
            'email' => 'client123456789'
        ]);
    }

    /** @test */
    public function it_creates_demo_user_successfully()
    {
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '987654321',
            'comment' => 'Demo user comment',
            'demo' => true
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'user' => [
                        'tg_id' => '987654321',
                        'email' => 'client987654321',
                        'demo_until' => null,
                    ],
                    'is_demo' => true,
                    'expiration' => null
                ]
            ]);
    }

    /** @test */
    public function it_creates_user_with_expiration()
    {
        $expiration = now()->addDays(7)->timestamp;

        $response = $this->postJson('/api/user/create', [
            'tg_id' => '111222333',
            'comment' => 'User with custom expiration',
            'demo' => false,
            'expiration' => $expiration
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'user' => [
                        'tg_id' => '111222333',
                        'email' => 'client111222333',
                        'demo_until' => null,
                    ],
                    'is_demo' => false,
                    'expiration' => $expiration
                ]
            ]);
    }

    /** @test */
    public function it_creates_demo_user_with_expiration()
    {
        $expiration = now()->addHours(12)->timestamp;

        $response = $this->postJson('/api/user/create', [
            'tg_id' => '444555666',
            'comment' => 'Demo user with expiration',
            'demo' => true,
            'expiration' => $expiration
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'user' => [
                        'tg_id' => '444555666',
                        'email' => 'client444555666',
                    ],
                    'is_demo' => true,
                    'expiration' => $expiration
                ]
            ]);

        // Check demo_until was set in database
        $user = User::where('tg_id', '444555666')->first();
        $this->assertNotNull($user->demo_until);
        $this->assertEquals($expiration, $user->demo_until->timestamp);
    }

    /** @test */
    public function it_returns_error_when_user_already_exists()
    {
        // Create existing user
        User::factory()->create([
            'tg_id' => '123456789',
            'email' => 'client123456789'
        ]);

        $response = $this->postJson('/api/user/create', [
            'tg_id' => '123456789',
            'comment' => 'Test comment',
            'demo' => false
        ]);

        $response->assertStatus(409)
            ->assertJson([
                'success' => false,
                'message' => 'User already exists'
            ]);
    }

    /** @test */
    public function it_validates_required_fields_for_user_creation()
    {
        $response = $this->postJson('/api/user/create', []);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => [
                    'tg_id' => ['The tg id field is required.'],
                    'comment' => ['The comment field is required.']
                ]
            ]);
    }

    /** @test */
    public function it_validates_tg_id_max_length()
    {
        $response = $this->postJson('/api/user/create', [
            'tg_id' => str_repeat('1', 256), // 256 characters
            'comment' => 'Test comment'
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => [
                    'tg_id' => ['The tg id field must not be greater than 255 characters.']
                ]
            ]);
    }

    /** @test */
    public function it_validates_comment_max_length()
    {
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '123456789',
            'comment' => str_repeat('a', 501) // 501 characters
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => [
                    'comment' => ['The comment field must not be greater than 500 characters.']
                ]
            ]);
    }

    /** @test */
    public function it_validates_expiration_must_be_positive()
    {
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '123456789',
            'comment' => 'Test comment',
            'expiration' => 0
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => [
                    'expiration' => ['The expiration field must be at least 1.']
                ]
            ]);
    }

    /** @test */
    public function it_validates_expiration_must_be_integer()
    {
        $response = $this->postJson('/api/user/create', [
            'tg_id' => '123456789',
            'comment' => 'Test comment',
            'expiration' => 'invalid'
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => [
                    'expiration' => ['The expiration field must be an integer.']
                ]
            ]);
    }
}
