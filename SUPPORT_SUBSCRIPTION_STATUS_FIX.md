# Исправление расчета статуса подписки в support/index.blade.php

## Проблема

### **Симптомы:**
- Неправильный расчет статуса подписки в support/index.blade.php
- Отсутствие проверки лимита трафика
- Неточное отображение времени истечения
- Несоответствие логике subscription-modern-simple.blade.php

### **Причина:**
В SupportController отсутствовала важная логика для проверки лимита трафика и правильного расчета статуса, которая есть в SubscriptionController.

## Исправления в SupportController

### **Было (неправильно):**
```php
// Упрощенная логика без проверки трафика
$isExpired = $user->expiry_time && $user->expiry_time->isPast();
$isDemo = !is_null($user->demo_until);

$status = 'Активна';
$statusColor = 'text-green-200';

if ($user->disabled_at) {
    $status = 'Отключена';
    $statusColor = 'text-red-200';
} elseif ($isExpired) {
    $status = 'Истекла';
    $statusColor = 'text-red-200';
} elseif ($isDemo) {
    $status = 'Демо';
    $statusColor = 'text-yellow-200';
}
```

### **Стало (правильно):**
```php
// Полная логика как в SubscriptionController
// Get statistics from User model (centralized source)
$totalUpload = $user->up_traffic;
$totalDownload = $user->down_traffic;
$totalLimit = $user->total_gb;
$earliestExpiry = $user->expiry_time;

// Determine status
$isExpired = $earliestExpiry && $earliestExpiry->isPast();
$isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
$isDemo = !is_null($user->demo_until);

$status = 'активна';
$statusColor = 'text-green-200';

if ($user->disabled_at) {
    $status = 'отключена';
    $statusColor = 'text-red-200';
} elseif ($isLimited) {
    $status = 'лимит исчерпан';
    $statusColor = 'text-yellow-200';
} elseif ($isExpired) {
    $status = 'истекла';
    $statusColor = 'text-red-200';
} elseif ($isDemo) {
    $status = 'демо';
    $statusColor = 'text-yellow-200';
}
```

### **Ключевые изменения:**
- ✅ **Добавлена проверка лимита трафика** - `$isLimited`
- ✅ **Используются данные из User модели** - централизованный источник
- ✅ **Правильный порядок проверок** - сначала disabled, потом limited, потом expired
- ✅ **Добавлены недостающие переменные** в compact()

## Исправления в шаблоне support/index.blade.php

### **Было (статичное отображение):**
```blade
<p class="text-sm font-medium text-white">
    Подписка <span class="{{ $statusColor }}">{{ $status }}</span>
</p>
@if($user->expiry_time)
    <p class="text-xs text-blue-100 mt-1">
        <i class="fas fa-calendar mr-1"></i>
        до {{ $user->expiry_time->format('d.m.Y H:i') }}
    </p>
    @if($daysUntilExpiry !== null)
        <p class="text-xs text-blue-100">
            <i class="fas fa-hourglass-half mr-1"></i>
            осталось {{ $daysUntilExpiry }} дней
        </p>
    @endif
@endif
```

### **Стало (динамическое отображение):**
```blade
<p class="text-sm font-medium text-white">
    Подписка <span class="{{ $statusColor }}">{{ $status }}</span>
    <span id="expiryDate" class="text-blue-100"></span>
</p>

<p id="remainingTime" class="text-xs text-blue-100 mt-1" style="display: none;"></p>
```

### **Добавлен JavaScript (как в subscription-modern-simple):**
```javascript
// Support page data
const supportData = {
    status: '{{ $status }}',
    expiryTime: '{{ $earliestExpiry ? $earliestExpiry->utc()->toISOString() : '' }}',
    isExpired: {{ $isExpired ? 'true' : 'false' }},
    isLimited: {{ $isLimited ? 'true' : 'false' }},
    isDemo: {{ $isDemo ? 'true' : 'false' }}
};

// Format expiry date (same as subscription-modern-simple)
function formatExpiryDate(isoString) { ... }

// Calculate remaining time (same as subscription-modern-simple)
function calculateRemainingTime(isoString) { ... }

// Initialize expiry display (same logic as subscription-modern-simple)
function initializeExpiryDisplay() {
    const expiryDateElement = document.getElementById('expiryDate');
    const remainingTimeElement = document.getElementById('remainingTime');

    if (supportData.expiryTime) {
        const formattedDate = formatExpiryDate(supportData.expiryTime);

        if (supportData.status === 'истекла') {
            expiryDateElement.textContent = formattedDate;
        } else {
            expiryDateElement.textContent = `до ${formattedDate}`;
        }

        const remaining = calculateRemainingTime(supportData.expiryTime);
        if (remaining && !remaining.expired && (supportData.status === 'активна' || supportData.status === 'демо')) {
            remainingTimeElement.textContent = `осталось ${remaining.display}`;
            remainingTimeElement.style.display = 'block';
        } else {
            remainingTimeElement.style.display = 'none';
        }
    }
}
```

## Логика определения статуса

### **Приоритет проверок (как в SubscriptionController):**
1. **disabled_at** → "отключена" (красный)
2. **isLimited** → "лимит исчерпан" (желтый)
3. **isExpired** → "истекла" (красный)
4. **isDemo** → "демо" (желтый)
5. **default** → "активна" (зеленый)

### **Проверка лимита трафика:**
```php
$isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
```

### **Статусы и цвета:**
```php
'активна' => 'text-green-200'
'отключена' => 'text-red-200'
'лимит исчерпан' => 'text-yellow-200'
'истекла' => 'text-red-200'
'демо' => 'text-yellow-200'
```

## Сравнение с subscription-modern-simple

### **Теперь обе страницы используют:**
- ✅ **Одинаковую логику расчета статуса**
- ✅ **Одинаковые функции форматирования времени**
- ✅ **Одинаковое отображение даты истечения**
- ✅ **Одинаковый расчет оставшегося времени**

### **Источники данных:**
- ✅ **User модель** - централизованный источник трафика
- ✅ **user->up_traffic, user->down_traffic** - актуальные данные
- ✅ **user->total_gb** - лимит трафика
- ✅ **user->expiry_time** - время истечения

## Примеры отображения

### **Активная подписка:**
```
Подписка активна до 31.12.2024 23:59
осталось 15 дн.
```

### **Истекшая подписка:**
```
Подписка истекла 25.12.2024 23:59
```

### **Лимит исчерпан:**
```
Подписка лимит исчерпан до 31.12.2024 23:59
```

### **Демо версия:**
```
Подписка демо до 31.12.2024 23:59
осталось 2 дн.
Демо версия
```

### **Отключена:**
```
Подписка отключена
```

## Переданные переменные в шаблон

### **Добавлены новые переменные:**
```php
return view('support.index', compact(
    'user',
    'clients',
    'status',
    'statusColor',
    'isExpired',
    'isDemo',
    'isLimited',        // Новая
    'daysUntilExpiry',
    'totalUpload',      // Новая
    'totalDownload',    // Новая
    'totalLimit',       // Новая
    'earliestExpiry',   // Новая
    'todayRating',
    'lastRating',
    'telegramSupport',
    'whatsappSupport',
    'telegramChannel'
));
```

## Результат исправления

### **Теперь support/index.blade.php:**
- ✅ **Правильно определяет статус** - учитывает лимит трафика
- ✅ **Корректно отображает время** - динамическое обновление
- ✅ **Соответствует subscription-modern-simple** - одинаковая логика
- ✅ **Показывает актуальные данные** - из централизованного источника

### **Исправленные проблемы:**
- ✅ Добавлена проверка лимита трафика
- ✅ Исправлен порядок проверок статуса
- ✅ Добавлено динамическое отображение времени
- ✅ Унифицирована логика с основной страницей подписки

Статус подписки теперь рассчитывается правильно и соответствует логике subscription-modern-simple!
