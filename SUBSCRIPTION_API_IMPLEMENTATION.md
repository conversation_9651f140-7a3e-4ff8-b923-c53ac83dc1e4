# Реализация Subscription API

## Что реализовано

### **1. API Контроллер**
**Файл:** `app/Http/Controllers/Api/SubscriptionApiController.php`

#### **Методы:**
- `getSubscriptionLink()` - получение ссылки на подписку
- `findUserByIdentifier()` - поиск пользователя по идентификатору
- `generateSubscriptionLink()` - генерация ссылки
- `info()` - информация об API

### **2. API Маршруты**
**Файл:** `routes/api.php`

#### **Endpoints:**
- `GET /api/subscription/info` - информация об API
- `POST /api/subscription/link` - получение ссылки на подписку

### **3. Конфигурация**
**Файл:** `bootstrap/app.php`
- Добавлено подключение API маршрутов

### **4. Тесты**
**Файл:** `tests/Feature/SubscriptionApiTest.php`
- Полное покрытие тестами всех сценариев

### **5. Документация**
**Файл:** `API_DOCUMENTATION.md`
- Подробная документация с примерами

## API Endpoints

### **1. GET /api/subscription/info**
Получение информации об API.

**Ответ:**
```json
{
    "success": true,
    "data": {
        "api_version": "1.0",
        "endpoints": {
            "subscription_link": {
                "method": "POST",
                "url": "/api/subscription/link",
                "description": "Get subscription link by user identifier",
                "parameters": {
                    "tg_id": "Telegram ID (optional)",
                    "email": "User email (optional)",
                    "client_id": "Client ID (optional)"
                }
            }
        }
    }
}
```

### **2. POST /api/subscription/link**
Получение ссылки на подписку по идентификатору пользователя.

**Параметры:**
- `tg_id` (string, optional) - Telegram ID
- `email` (string, optional) - Email пользователя
- `client_id` (string, optional) - Client ID

**Примечание:** Должен быть указан хотя бы один параметр.

**Успешный ответ:**
```json
{
    "success": true,
    "data": {
        "subscription_link": "https://domain.com/subs/uuid/modern",
        "uuid": "550e8400-e29b-41d4-a716-************",
        "user": {
            "id": 1,
            "email": "client12345",
            "tg_id": "123456789",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        }
    }
}
```

## Логика поиска пользователей

### **Приоритет поиска:**
1. **По Telegram ID** (`tg_id`) - прямое совпадение в `users.tg_id`
2. **По Email** (`email`) - прямое совпадение в `users.email`
3. **По Client ID** (`client_id`) - преобразование в `client{client_id}` и поиск в `users.email`

### **Примеры:**
```php
// Поиск по tg_id
User::where('tg_id', '123456789')->first();

// Поиск по email
User::where('email', '<EMAIL>')->first();

// Поиск по client_id
User::where('email', 'client12345')->first(); // для client_id = "12345"
```

## Примеры использования

### **cURL:**
```bash
# Получение ссылки по Telegram ID
curl -X POST "https://domain.com/api/subscription/link" \
     -H "Content-Type: application/json" \
     -d '{"tg_id": "123456789"}'

# Получение ссылки по Email
curl -X POST "https://domain.com/api/subscription/link" \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'

# Получение ссылки по Client ID
curl -X POST "https://domain.com/api/subscription/link" \
     -H "Content-Type: application/json" \
     -d '{"client_id": "12345"}'
```

### **PHP (Laravel Http):**
```php
use Illuminate\Support\Facades\Http;

$response = Http::post('https://domain.com/api/subscription/link', [
    'tg_id' => '123456789'
]);

if ($response->successful()) {
    $data = $response->json();
    $link = $data['data']['subscription_link'];
    echo "Subscription link: " . $link;
}
```

### **JavaScript (Fetch):**
```javascript
fetch('https://domain.com/api/subscription/link', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        email: '<EMAIL>'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Link:', data.data.subscription_link);
    }
});
```

## Обработка ошибок

### **400 - Validation Error:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "email": ["The email field must be a valid email address."]
    }
}
```

### **400 - Missing Parameters:**
```json
{
    "success": false,
    "message": "At least one identifier (tg_id, email, or client_id) must be provided"
}
```

### **404 - User Not Found:**
```json
{
    "success": false,
    "message": "User not found"
}
```

### **500 - Server Error:**
```json
{
    "success": false,
    "message": "Internal server error"
}
```

## Логирование

### **Успешные запросы:**
```
[INFO] Subscription link requested
{
    "user_id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "tg_id": "123456789",
    "email": null,
    "client_id": null,
    "link": "https://domain.com/subs/550e8400-e29b-41d4-a716-************/modern"
}
```

### **Ошибки:**
```
[ERROR] Error getting subscription link
{
    "error": "Database connection failed",
    "trace": "...",
    "request": {"tg_id": "123456789"}
}
```

## Тестирование

### **Запуск тестов:**
```bash
php artisan test tests/Feature/SubscriptionApiTest.php
```

### **Покрытие тестами:**
- ✅ Получение информации об API
- ✅ Поиск по Telegram ID
- ✅ Поиск по Email
- ✅ Поиск по Client ID
- ✅ Валидация параметров
- ✅ Обработка отсутствующих пользователей
- ✅ Приоритет поиска
- ✅ Обработка ошибок

## Безопасность

### **Текущая реализация:**
- Валидация входных данных
- Логирование всех запросов
- Обработка исключений

### **Планируемые улучшения:**
- API ключи для аутентификации
- Rate limiting
- IP whitelist
- Request signing

## Расширение API

### **Планируемые endpoints:**
```php
// Получение статуса подписки
GET /api/subscription/{uuid}/status

// Создание пользователя
POST /api/user/create

// Обновление пользователя
PUT /api/user/{id}

// Bulk операции
POST /api/subscription/bulk/links

// Webhook уведомления
POST /api/webhook/subscription/updated
```

### **Добавление нового endpoint:**
1. Добавить метод в `SubscriptionApiController`
2. Добавить маршрут в `routes/api.php`
3. Написать тесты
4. Обновить документацию

## Мониторинг

### **Метрики для отслеживания:**
- Количество запросов к API
- Время ответа
- Количество ошибок
- Популярные методы поиска (tg_id vs email vs client_id)

### **Логи для анализа:**
```bash
# Успешные запросы
grep "Subscription link requested" storage/logs/laravel.log

# Ошибки API
grep "Error getting subscription link" storage/logs/laravel.log

# Не найденные пользователи
grep "User not found" storage/logs/laravel.log
```

Теперь у вас есть полнофункциональный API для получения ссылок на подписки!
