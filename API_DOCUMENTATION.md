# Subscription API Documentation

## Base URL
```
https://your-domain.com/api
```

## Endpoints

### 1. Get API Information
Get information about available API endpoints.

**Endpoint:** `GET /api/subscription/info`

**Response:**
```json
{
    "success": true,
    "data": {
        "api_version": "1.0",
        "endpoints": {
            "subscription_link": {
                "method": "POST",
                "url": "/api/subscription/link",
                "description": "Get subscription link by user identifier",
                "parameters": {
                    "tg_id": "Telegram ID (optional)",
                    "email": "User email (optional)",
                    "client_id": "Client ID (optional)"
                },
                "note": "At least one parameter must be provided"
            }
        }
    }
}
```

### 2. Create New User
Create a new user with XUI clients on all available servers.

**Endpoint:** `POST /api/user/create`

**Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Request Body:**
```json
{
    "tg_id": "123456789",
    "comment": "User comment or description",
    "demo": false,
    "expiration": 1735689600
}
```

**Parameters:**
- `tg_id` (string, required): Telegram ID of the user
- `comment` (string, required): Comment or description for the user (max 500 characters)
- `demo` (boolean, optional): Demo access flag (default: false)
- `expiration` (integer, optional): Unix timestamp for expiration date

**Success Response (201):**
```json
{
    "success": true,
    "message": "User created successfully",
    "data": {
        "user": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "email": "client123456789",
            "tg_id": "123456789",
            "demo_until": "2024-01-08T00:00:00.000000Z",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        },
        "subscription_link": "https://your-domain.com/subs/550e8400-e29b-41d4-a716-************/modern",
        "clients_created": 3,
        "clients_failed": 0,
        "created_clients": [
            {
                "server": "Server 1",
                "inbound_id": 1,
                "xui_client_id": 1
            },
            {
                "server": "Server 2",
                "inbound_id": 2,
                "xui_client_id": 2
            }
        ],
        "failed_clients": [],
        "is_demo": false,
        "expiration": 1735689600
    }
}
```

**Error Responses:**

**400 - Validation Error:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "tg_id": ["The tg id field is required."],
        "comment": ["The comment field is required."]
    }
}
```

**409 - User Already Exists:**
```json
{
    "success": false,
    "message": "User already exists",
    "data": {
        "existing_user": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "email": "client123456789",
            "tg_id": "123456789"
        }
    }
}
```

**500 - Server Error:**
```json
{
    "success": false,
    "message": "Internal server error"
}
```

### 3. Get Subscription Link
Get subscription link by user identifier (tg_id, email, or client_id).

**Endpoint:** `POST /api/subscription/link`

**Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Request Body:**
```json
{
    "tg_id": "123456789",
    "email": "<EMAIL>",
    "client_id": "12345"
}
```

**Note:** At least one of the parameters (`tg_id`, `email`, or `client_id`) must be provided.

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "subscription_link": "https://your-domain.com/subs/550e8400-e29b-41d4-a716-************/modern",
        "uuid": "550e8400-e29b-41d4-a716-************",
        "user": {
            "id": 1,
            "email": "client12345",
            "tg_id": "123456789",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        }
    }
}
```

**Error Responses:**

**400 - Validation Error:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "email": ["The email must be a valid email address."]
    }
}
```

**400 - Missing Parameters:**
```json
{
    "success": false,
    "message": "At least one identifier (tg_id, email, or client_id) must be provided"
}
```

**404 - User Not Found:**
```json
{
    "success": false,
    "message": "User not found"
}
```

**500 - Server Error:**
```json
{
    "success": false,
    "message": "Internal server error"
}
```

## Usage Examples

### cURL Examples

#### Get API Info:
```bash
curl -X GET "https://your-domain.com/api/subscription/info" \
     -H "Accept: application/json"
```

#### Get Subscription Link by Telegram ID:
```bash
curl -X POST "https://your-domain.com/api/subscription/link" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{"tg_id": "123456789"}'
```

#### Get Subscription Link by Email:
```bash
curl -X POST "https://your-domain.com/api/subscription/link" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{"email": "<EMAIL>"}'
```

#### Get Subscription Link by Client ID:
```bash
curl -X POST "https://your-domain.com/api/subscription/link" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{"client_id": "12345"}'
```

#### Create New User:
```bash
curl -X POST "https://your-domain.com/api/user/create" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{"tg_id": "123456789", "comment": "New user", "demo": false}'
```

#### Create Demo User:
```bash
curl -X POST "https://your-domain.com/api/user/create" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{"tg_id": "987654321", "comment": "Demo user", "demo": true}'
```

#### Create User with Custom Expiration:
```bash
curl -X POST "https://your-domain.com/api/user/create" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{"tg_id": "111222333", "comment": "User with expiration", "expiration": 1735689600}'
```

#### Create Demo User with Expiration:
```bash
curl -X POST "https://your-domain.com/api/user/create" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{"tg_id": "444555666", "comment": "Demo with expiration", "demo": true, "expiration": 1735689600}'
```

### PHP Examples

#### Using Guzzle:
```php
use GuzzleHttp\Client;

$client = new Client();

// Get subscription link by tg_id
$response = $client->post('https://your-domain.com/api/subscription/link', [
    'json' => [
        'tg_id' => '123456789'
    ],
    'headers' => [
        'Accept' => 'application/json',
        'Content-Type' => 'application/json'
    ]
]);

$data = json_decode($response->getBody(), true);

if ($data['success']) {
    $subscriptionLink = $data['data']['subscription_link'];
    echo "Subscription link: " . $subscriptionLink;
} else {
    echo "Error: " . $data['message'];
}
```

#### Using Laravel Http Client:
```php
use Illuminate\Support\Facades\Http;

$response = Http::post('https://your-domain.com/api/subscription/link', [
    'tg_id' => '123456789'
]);

if ($response->successful()) {
    $data = $response->json();
    $subscriptionLink = $data['data']['subscription_link'];
    echo "Subscription link: " . $subscriptionLink;
} else {
    echo "Error: " . $response->json()['message'];
}
```

### JavaScript Examples

#### Using Fetch:
```javascript
// Get subscription link by email
fetch('https://your-domain.com/api/subscription/link', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    body: JSON.stringify({
        email: '<EMAIL>'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Subscription link:', data.data.subscription_link);
    } else {
        console.error('Error:', data.message);
    }
})
.catch(error => {
    console.error('Network error:', error);
});
```

#### Using Axios:
```javascript
const axios = require('axios');

async function getSubscriptionLink(clientId) {
    try {
        const response = await axios.post('https://your-domain.com/api/subscription/link', {
            client_id: clientId
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        if (response.data.success) {
            return response.data.data.subscription_link;
        } else {
            throw new Error(response.data.message);
        }
    } catch (error) {
        console.error('Error getting subscription link:', error.message);
        throw error;
    }
}

// Usage
getSubscriptionLink('12345')
    .then(link => console.log('Subscription link:', link))
    .catch(error => console.error('Failed to get link:', error.message));
```

## User Identification Logic

The API searches for users in the following order:

1. **By Telegram ID** (`tg_id`): Direct match in `users.tg_id` field
2. **By Email** (`email`): Direct match in `users.email` field  
3. **By Client ID** (`client_id`): Converts to email format `client{client_id}` and searches in `users.email` field

## Response Format

All API responses follow this format:

```json
{
    "success": boolean,
    "message": "string (optional, usually for errors)",
    "data": object (optional, contains response data),
    "errors": object (optional, validation errors)
}
```

## Error Handling

- **400**: Bad Request (validation errors, missing parameters)
- **404**: Not Found (user not found)
- **500**: Internal Server Error (unexpected errors)

All errors are logged with request details for debugging.

## Future Extensions

This API is designed for easy extension. Planned features:

- Authentication/API keys
- Rate limiting
- Subscription status information
- Bulk operations
- Webhook notifications
- User creation endpoints
