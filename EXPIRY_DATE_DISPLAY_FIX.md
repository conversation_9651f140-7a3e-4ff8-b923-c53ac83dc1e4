# Исправление отображения даты истечения подписки

## Проблема
Когда подписка истекла, отображалось "Подписка истекла до 25.12.2024", что выглядело неправильно.

## Решение
Убрано слово "до" для истекших подписок.

## Изменения в коде

### **1. subscription-modern.blade.php (React компонент)**

#### **Было:**
```jsx
<p className={`text-sm font-medium ${getStatusColor()}`}>
    Подписка {getStatusText().toLowerCase()}
    {subscriptionData.expiryTime && ` до ${formatExpiryDate()}`}
</p>
```

#### **Стало:**
```jsx
<p className={`text-sm font-medium ${getStatusColor()}`}>
    Подписка {getStatusText().toLowerCase()}
    {subscriptionData.expiryTime && subscriptionData.status !== 'expired' && ` до ${formatExpiryDate()}`}
    {subscriptionData.expiryTime && subscriptionData.status === 'expired' && ` ${formatExpiryDate()}`}
</p>
```

### **2. subscription-modern-simple.blade.php (JavaScript)**

#### **Было:**
```javascript
function updateTimeDisplay() {
    // ...
    if (subscriptionData.expiryTime) {
        const formattedDate = formatExpiryDate(subscriptionData.expiryTime);
        expiryDateElement.textContent = `до ${formattedDate}`;
        // ...
    }
}
```

#### **Стало:**
```javascript
function updateTimeDisplay() {
    // ...
    if (subscriptionData.expiryTime) {
        const formattedDate = formatExpiryDate(subscriptionData.expiryTime);
        
        // Show "до" only if subscription is not expired
        if (subscriptionData.status === 'expired') {
            expiryDateElement.textContent = formattedDate;
        } else {
            expiryDateElement.textContent = `до ${formattedDate}`;
        }
        // ...
    }
}
```

## Логика отображения

### **Активная подписка:**
```
Подписка активна до 25.12.2024, 15:30 MSK
осталось 15 дн.
```

### **Истекшая подписка:**
```
Подписка истекла 23.12.2024, 15:30 MSK
```

### **Лимит исчерпан:**
```
Подписка лимит исчерпан до 25.12.2024, 15:30 MSK
```

### **Неизвестный статус:**
```
Подписка неизвестно до 25.12.2024, 15:30 MSK
```

## Условия отображения

### **Показывать "до":**
- `status !== 'expired'` И `expiryTime` существует

### **Не показывать "до":**
- `status === 'expired'` И `expiryTime` существует

### **Не показывать дату:**
- `expiryTime` не существует или пустое

## Примеры по статусам

### **1. Активная подписка (active):**
```
✅ Подписка активна до 25.12.2024, 15:30 MSK
   осталось 15 дн.
```

### **2. Истекшая подписка (expired):**
```
❌ Подписка истекла 23.12.2024, 15:30 MSK
```

### **3. Лимит исчерпан (limited):**
```
⚠️ Подписка лимит исчерпан до 25.12.2024, 15:30 MSK
```

### **4. Неизвестный статус:**
```
❓ Подписка неизвестно до 25.12.2024, 15:30 MSK
```

### **5. Без даты истечения:**
```
✅ Подписка активна
```

## Техническая реализация

### **React компонент (subscription-modern.blade.php):**
```jsx
// Условное отображение с проверкой статуса
{subscriptionData.expiryTime && subscriptionData.status !== 'expired' && ` до ${formatExpiryDate()}`}
{subscriptionData.expiryTime && subscriptionData.status === 'expired' && ` ${formatExpiryDate()}`}
```

### **JavaScript (subscription-modern-simple.blade.php):**
```javascript
// Условная логика в функции updateTimeDisplay
if (subscriptionData.status === 'expired') {
    expiryDateElement.textContent = formattedDate;
} else {
    expiryDateElement.textContent = `до ${formattedDate}`;
}
```

## Тестирование

### **1. Проверка активной подписки:**
```
1. Установите status: 'active' в данных
2. Установите expiryTime в будущем
3. Должно показать: "Подписка активна до [дата]"
```

### **2. Проверка истекшей подписки:**
```
1. Установите status: 'expired' в данных
2. Установите expiryTime в прошлом
3. Должно показать: "Подписка истекла [дата]"
```

### **3. Проверка без даты:**
```
1. Установите expiryTime: null или undefined
2. Должно показать только: "Подписка [статус]"
```

### **4. Проверка лимита:**
```
1. Установите status: 'limited' в данных
2. Установите expiryTime в будущем
3. Должно показать: "Подписка лимит исчерпан до [дата]"
```

## Обновленные файлы

### **1. resources/views/subs/subscription-modern.blade.php**
- Строки 130-133: Обновлена логика отображения даты
- Добавлены условия для проверки статуса expired

### **2. resources/views/subs/subscription-modern-simple.blade.php**
- Строки 413-433: Обновлена функция updateTimeDisplay()
- Добавлена проверка статуса перед добавлением "до"

## Совместимость

### **Обратная совместимость:**
- Если status не определен, работает как раньше (показывает "до")
- Если expiryTime не определен, дата не показывается
- Все существующие статусы поддерживаются

### **Новое поведение:**
- Только для status === 'expired' убирается "до"
- Все остальные статусы показывают "до" как раньше

## Результат

### **До исправления:**
```
❌ Подписка истекла до 23.12.2024, 15:30 MSK  // Неправильно
```

### **После исправления:**
```
❌ Подписка истекла 23.12.2024, 15:30 MSK     // Правильно
```

Теперь отображение даты истечения корректно для всех статусов подписки!
