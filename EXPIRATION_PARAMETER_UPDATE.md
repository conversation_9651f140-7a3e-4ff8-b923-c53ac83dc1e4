# Обновление API для поддержки expiration параметра

## Что добавлено

### **1. Новый параметр API**
- **Параметр**: `expiration` (integer, optional)
- **Описание**: Unix timestamp для даты истечения
- **Валидация**: Должен быть положительным целым числом

### **2. Новое поле в таблице users**
- **Поле**: `demo_until` (timestamp, nullable)
- **Назначение**: Хранение даты истечения для demo пользователей
- **Заполняется**: Только если `demo = true` И `expiration` указан

### **3. Обновленная логика создания клиентов**
- **Если expiration указан**: Используется переданное значение
- **Если expiration НЕ указан**: Используется дефолтная логика (demo: 1 день, обычный: 30 дней)

## Изменения в коде

### **1. Миграция базы данных**
**Файл:** `database/migrations/2024_12_30_000001_add_demo_until_to_users_table.php`
```php
Schema::table('users', function (Blueprint $table) {
    $table->timestamp('demo_until')->nullable()->after('tg_id');
});
```

### **2. Модель User**
**Файл:** `app/Models/User.php`
```php
// Добавлено в fillable
'demo_until',

// Добавлено в casts
'demo_until' => 'datetime',
```

### **3. XuiApiService**
**Файл:** `app/Services/XuiApiService.php`
```php
// Обновленная сигнатура метода
public function createClient(XuiServer $server, XuiInbound $inbound, $user, string $comment, bool $isDemo = false, ?int $expiration = null): bool

// Обновленная логика расчета времени истечения
if ($expiration) {
    // Use provided expiration timestamp
    $expirationTimestamp = $expiration * 1000; // X-UI expects milliseconds
} else {
    // Default: demo: 1 day, regular: 30 days
    $expirationDays = $isDemo ? 1 : 30;
    $expirationTimestamp = now()->addDays($expirationDays)->timestamp * 1000;
}
```

### **4. API Контроллер**
**Файл:** `app/Http/Controllers/Api/SubscriptionApiController.php`

#### **Валидация:**
```php
'expiration' => 'nullable|integer|min:1',
```

#### **Создание пользователя:**
```php
// Add demo_until if demo flag is set and expiration is provided
if ($isDemo && $expiration) {
    $userData['demo_until'] = \Carbon\Carbon::createFromTimestamp($expiration);
}
```

#### **Вызов createClient:**
```php
$success = $xuiApiService->createClient($server, $inbound, $user, $comment, $isDemo, $expiration);
```

## Примеры использования

### **1. Создание пользователя с кастомным expiration:**
```bash
curl -X POST "/api/user/create" \
     -H "Content-Type: application/json" \
     -d '{
       "tg_id": "123456789",
       "comment": "Пользователь на 7 дней",
       "demo": false,
       "expiration": 1735689600
     }'
```

### **2. Создание demo пользователя с expiration:**
```bash
curl -X POST "/api/user/create" \
     -H "Content-Type: application/json" \
     -d '{
       "tg_id": "987654321",
       "comment": "Демо на 12 часов",
       "demo": true,
       "expiration": 1735646400
     }'
```

### **3. Создание пользователя без expiration (дефолтная логика):**
```bash
curl -X POST "/api/user/create" \
     -H "Content-Type: application/json" \
     -d '{
       "tg_id": "111222333",
       "comment": "Обычный пользователь",
       "demo": false
     }'
```

## Логика работы

### **Приоритет использования expiration:**
1. **Если expiration указан**: Используется переданное значение для всех серверов
2. **Если expiration НЕ указан**: Используется дефолтная логика:
   - Demo пользователь: 1 день
   - Обычный пользователь: 30 дней

### **Заполнение demo_until:**
- **Заполняется**: Только если `demo = true` И `expiration` указан
- **НЕ заполняется**: Если `demo = false` или `expiration` не указан

### **Примеры сценариев:**

#### **Сценарий 1: Demo + expiration**
```json
{
    "demo": true,
    "expiration": 1735689600
}
```
- ✅ `demo_until` = 2024-01-01 00:00:00
- ✅ X-UI клиенты создаются с expiration = 1735689600

#### **Сценарий 2: Demo без expiration**
```json
{
    "demo": true
}
```
- ❌ `demo_until` = null
- ✅ X-UI клиенты создаются с expiration = now() + 1 день

#### **Сценарий 3: Обычный + expiration**
```json
{
    "demo": false,
    "expiration": 1735689600
}
```
- ❌ `demo_until` = null
- ✅ X-UI клиенты создаются с expiration = 1735689600

#### **Сценарий 4: Обычный без expiration**
```json
{
    "demo": false
}
```
- ❌ `demo_until` = null
- ✅ X-UI клиенты создаются с expiration = now() + 30 дней

## Обновленный ответ API

```json
{
    "success": true,
    "message": "User created successfully",
    "data": {
        "user": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "email": "client123456789",
            "tg_id": "123456789",
            "demo_until": "2024-01-08T00:00:00.000000Z",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        },
        "subscription_link": "https://domain.com/subs/uuid/modern",
        "clients_created": 3,
        "clients_failed": 0,
        "created_clients": [...],
        "failed_clients": [],
        "is_demo": true,
        "expiration": 1735689600
    }
}
```

## Валидация

### **Правила валидации:**
- `expiration` должен быть целым числом
- `expiration` должен быть больше 0
- `expiration` опционален

### **Ошибки валидации:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "expiration": ["The expiration field must be at least 1."]
    }
}
```

## Тестирование

### **Новые тесты:**
- ✅ Создание пользователя с expiration
- ✅ Создание demo пользователя с expiration
- ✅ Валидация expiration (положительное число)
- ✅ Валидация expiration (целое число)
- ✅ Проверка заполнения demo_until

### **Запуск тестов:**
```bash
php artisan test tests/Feature/SubscriptionApiTest.php
```

## Миграция

### **Запуск миграции:**
```bash
php artisan migrate
```

### **Откат миграции (если нужно):**
```bash
php artisan migrate:rollback
```

## Логирование

### **Обновленное логирование:**
```
[INFO] User created
{
    "user_id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "tg_id": "123456789",
    "email": "client123456789",
    "is_demo": true,
    "expiration": 1735689600,
    "demo_until": "2024-01-01T00:00:00.000000Z"
}
```

## Обратная совместимость

### **Полная обратная совместимость:**
- ✅ Существующие запросы без `expiration` работают как раньше
- ✅ Дефолтная логика (1 день для demo, 30 дней для обычных) сохранена
- ✅ Новое поле `demo_until` nullable, не влияет на существующих пользователей

Теперь API поддерживает гибкую настройку времени истечения подписки!
