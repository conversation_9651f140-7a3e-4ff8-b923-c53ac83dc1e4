# Исправление функции toggleAccordion для работы с множественными аккордионами

## Проблема

### **Исходные проблемы:**
- ❌ Функция `toggleAccordion()` работала только с одним аккордионом
- ❌ Использовались фиксированные ID элементов (`accordionContent`, `accordionIcon`)
- ❌ Глобальная переменная `isAccordionOpen` не подходила для множественных аккордионов
- ❌ Неправильная логика в обработчике событий (`length === 0` вместо `> 0`)

### **Симптомы:**
- Только один аккордион мог работать на странице
- При добавлении новых аккордионов они не функционировали
- Состояние аккордионов конфликтовало между собой

## Решение

### **1. Переписана функция toggleAccordion()**

#### **Было (неправильно):**
```javascript
function toggleAccordion() {
    console.log('toggleAccordion called, current state:', isAccordionOpen);

    isAccordionOpen = !isAccordionOpen;
    const content = document.getElementById('accordionContent');
    const icon = document.getElementById('accordionIcon');

    // ... фиксированные ID элементов
}
```

#### **Стало (правильно):**
```javascript
function toggleAccordion(button) {
    console.log('toggleAccordion called for button:', button);

    // Динамическое определение связанных элементов
    const buttonId = button.id;
    const contentId = buttonId.replace('Button', 'Content');
    const iconId = buttonId.replace('Button', 'Icon');
    
    const content = document.getElementById(contentId);
    const icon = document.getElementById(iconId);

    // Определение текущего состояния из DOM
    const isCurrentlyOpen = content.classList.contains('open') || content.style.display === 'block';

    // Переключение состояния
    if (!isCurrentlyOpen) {
        // Открытие аккордиона
        content.classList.remove('closed', 'hidden');
        content.classList.add('open', 'visible');
        content.style.display = 'block';
        content.style.maxHeight = '1000px';
        content.style.opacity = '1';

        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        // Закрытие аккордиона
        content.classList.remove('open', 'visible');
        content.classList.add('closed', 'hidden');
        content.style.display = 'none';
        content.style.maxHeight = '0';
        content.style.opacity = '0';

        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}
```

### **2. Исправлены обработчики событий**

#### **Было (неправильно):**
```javascript
const accordionButtons = document.querySelectorAll('button[id^="accordionButton"]');
if (accordionButtons.length === 0) { // ❌ Неправильная логика
    accordionButtons.forEach(accordionButton => {
        accordionButton.addEventListener('click', function(e) {
            e.preventDefault();
            toggleAccordion(); // ❌ Не передается кнопка
        });
    });
} else {
    console.error('Accordion button not found for event listener!');
}
```

#### **Стало (правильно):**
```javascript
const accordionButtons = document.querySelectorAll('button[id^="accordionButton"]');
console.log('Found accordion buttons:', accordionButtons.length);

if (accordionButtons.length > 0) { // ✅ Правильная логика
    console.log('Setting up accordion button event listeners');
    accordionButtons.forEach(accordionButton => {
        console.log('Adding event listener to button:', accordionButton.id);
        accordionButton.addEventListener('click', function(e) {
            console.log('Accordion button clicked:', this.id);
            e.preventDefault();
            toggleAccordion(this); // ✅ Передается кнопка
        });
    });
} else {
    console.warn('No accordion buttons found for event listeners!');
}
```

### **3. Удалена глобальная переменная**

#### **Было:**
```javascript
let isAccordionOpen = false; // ❌ Глобальное состояние
```

#### **Стало:**
```javascript
// ✅ Состояние определяется из DOM элементов
const isCurrentlyOpen = content.classList.contains('open') || content.style.display === 'block';
```

## Логика работы

### **Соглашение об именовании:**
- **Кнопка:** `accordionButton{Name}` (например: `accordionButtonAdvanced`)
- **Контент:** `accordionContent{Name}` (например: `accordionContentAdvanced`)
- **Иконка:** `accordionIcon{Name}` (например: `accordionIconAdvanced`)

### **Алгоритм функции:**
1. **Получить ID кнопки** - `button.id`
2. **Вычислить ID связанных элементов** - заменить "Button" на "Content"/"Icon"
3. **Найти элементы в DOM** - `getElementById()`
4. **Определить текущее состояние** - проверить классы и стили
5. **Переключить состояние** - добавить/удалить классы и стили

### **Определение состояния:**
```javascript
const isCurrentlyOpen = content.classList.contains('open') || content.style.display === 'block';
```

### **Переключение состояния:**
- **Открытие:** добавить классы `open`, `visible` + стили
- **Закрытие:** добавить классы `closed`, `hidden` + стили
- **Иконка:** переключить между `fa-chevron-down` и `fa-chevron-up`

## Примеры использования

### **HTML структура:**
```html
<!-- Первый аккордион -->
<button id="accordionButtonAdvanced">
    <i id="accordionIconAdvanced" class="fas fa-chevron-down"></i>
    Дополнительно
</button>
<div id="accordionContentAdvanced" class="closed hidden">
    <!-- Контент первого аккордиона -->
</div>

<!-- Второй аккордион -->
<button id="accordionButtonSettings">
    <i id="accordionIconSettings" class="fas fa-chevron-down"></i>
    Настройки
</button>
<div id="accordionContentSettings" class="closed hidden">
    <!-- Контент второго аккордиона -->
</div>
```

### **JavaScript инициализация:**
```javascript
// Автоматически найдет все кнопки с ID начинающимся на "accordionButton"
const accordionButtons = document.querySelectorAll('button[id^="accordionButton"]');
accordionButtons.forEach(accordionButton => {
    accordionButton.addEventListener('click', function(e) {
        e.preventDefault();
        toggleAccordion(this); // Передаем кнопку в функцию
    });
});
```

## Преимущества нового решения

### **1. Масштабируемость:**
- ✅ Поддержка неограниченного количества аккордионов
- ✅ Автоматическое обнаружение новых аккордионов
- ✅ Независимое состояние каждого аккордиона

### **2. Гибкость:**
- ✅ Динамическое определение связанных элементов
- ✅ Соглашение об именовании вместо жестких ID
- ✅ Легкое добавление новых аккордионов

### **3. Надежность:**
- ✅ Состояние определяется из DOM, а не из переменных
- ✅ Проверка существования элементов
- ✅ Подробное логирование для отладки

### **4. Производительность:**
- ✅ Убрана глобальная переменная состояния
- ✅ Минимальные DOM операции
- ✅ Эффективный поиск элементов

## Отладка

### **Логирование:**
```javascript
console.log('toggleAccordion called for button:', button);
console.log('Looking for content:', contentId, 'icon:', iconId);
console.log('Current state - isOpen:', isCurrentlyOpen);
console.log('Accordion classes after toggle:', content.className);
```

### **Проверка элементов:**
```javascript
if (!content || !icon) {
    console.error('Accordion elements not found! Content:', contentId, 'Icon:', iconId);
    return;
}
```

### **Проверка инициализации:**
```javascript
console.log('Found accordion buttons:', accordionButtons.length);
accordionButtons.forEach(button => {
    console.log('Button found:', button.id);
});
```

## Совместимость

### **Обратная совместимость:**
- ✅ Работает с существующими аккордионами
- ✅ Сохранены все CSS классы и стили
- ✅ Не требует изменения HTML структуры

### **Требования:**
- ✅ Соблюдение соглашения об именовании ID
- ✅ Наличие соответствующих CSS классов
- ✅ Правильная HTML структура

Функция `toggleAccordion()` теперь корректно работает с множественными аккордионами!
