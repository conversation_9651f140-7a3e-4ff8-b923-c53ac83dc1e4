# Обновленная функциональность рейтингов

## Изменения в логике оценок

### **Новая логика:**
- ✅ Пользователь может изменять оценку в течение дня
- ✅ Каждый день создается новая запись
- ✅ В течение дня обновляется существующая запись
- ✅ Показывается текущая оценка пользователя

### **Было:**
- ❌ Одна оценка в день (без возможности изменения)
- ❌ Блокировка кнопок после оценки

### **Стало:**
- ✅ Можно изменять оценку в течение дня
- ✅ Кнопки остаются активными
- ✅ Показывается текущая оценка
- ✅ Уведомления об обновлении

## Изменения в модели UserRating

### **Удаленные методы:**
```php
// Удален метод canUserRateToday()
public static function canUserRateToday(int $userId): bool
```

### **Новые методы:**
```php
// Получить оценку пользователя за сегодня
public static function getUserTodayRating(int $userId): ?self

// Создать или обновить оценку за сегодня
public static function createOrUpdateTodayRating(int $userId, int $rating, ?string $userIp = null, ?string $userAgent = null): self
```

### **Логика createOrUpdateTodayRating:**
```php
$today = now()->startOfDay();
$tomorrow = now()->addDay()->startOfDay();

$existingRating = self::where('user_id', $userId)
    ->where('created_at', '>=', $today)
    ->where('created_at', '<', $tomorrow)
    ->first();

if ($existingRating) {
    // Обновляем существующую оценку
    $existingRating->update([
        'rating' => $rating,
        'user_ip' => $userIp,
        'user_agent' => $userAgent,
        'updated_at' => now(),
    ]);
    return $existingRating;
} else {
    // Создаем новую оценку
    return self::create([...]);
}
```

## Изменения в контроллере SupportController

### **Метод index():**
```php
// Было:
$canRate = UserRating::canUserRateToday($user->id);

// Стало:
$todayRating = UserRating::getUserTodayRating($user->id);
```

### **Метод submitRating():**
```php
// Было:
if (!UserRating::canUserRateToday($user->id)) {
    return response()->json(['success' => false, 'message' => 'Вы уже оценили сервис сегодня']);
}
$rating = UserRating::create([...]);

// Стало:
$rating = UserRating::createOrUpdateTodayRating(
    $user->id,
    $request->rating,
    $request->ip(),
    $request->userAgent()
);

$isUpdate = $rating->wasRecentlyCreated ? false : true;
$message = $isUpdate 
    ? "Оценка обновлена! {$emoji} {$label}"
    : "Спасибо за оценку! {$emoji} {$label}";
```

## Изменения в шаблоне

### **Отображение текущей оценки:**
```blade
@if($todayRating)
    <p class="text-sm text-blue-400">
        <i class="fas fa-edit mr-1"></i>
        Сегодня вы поставили: {{ $todayRating->emoji }} {{ $todayRating->label }}
        <br><span class="text-xs text-gray-400">Можете изменить оценку</span>
    </p>
@endif
```

### **JavaScript изменения:**
```javascript
// Было:
const canRate = {{ $canRate ? 'true' : 'false' }};
if (!canRate) {
    button.style.opacity = '0.5';
    button.style.cursor = 'not-allowed';
    return;
}

// Стало:
const todayRating = {{ $todayRating ? $todayRating->rating : 'null' }};

// Подсветка текущей оценки
if (todayRating) {
    const currentButton = document.querySelector(`[data-rating="${todayRating}"]`);
    if (currentButton) {
        currentButton.classList.add('selected');
    }
}
```

### **Обновление статуса после оценки:**
```javascript
// Было:
// Отключение кнопок после оценки
ratingButtons.forEach(btn => {
    btn.style.opacity = '0.5';
    btn.style.cursor = 'not-allowed';
    btn.onclick = null;
});

// Стало:
// Обновление статусного сообщения
statusMessage.innerHTML = `
    <i class="fas fa-edit mr-1"></i>
    Сегодня вы поставили: ${data.data.emoji} ${data.data.label}
    <br><span class="text-xs text-gray-400">Можете изменить оценку</span>
`;
```

## Пользовательский опыт

### **Сценарий использования:**

#### **1. Первая оценка дня:**
```
Пользователь → Нажимает 😄 → "Спасибо за оценку! 😄 Отлично"
Статус: "Сегодня вы поставили: 😄 Отлично. Можете изменить оценку"
```

#### **2. Изменение оценки:**
```
Пользователь → Нажимает 😐 → "Оценка обновлена! 😐 Нормально"
Статус: "Сегодня вы поставили: 😐 Нормально. Можете изменить оценку"
```

#### **3. Следующий день:**
```
Пользователь → Нажимает 🙂 → "Спасибо за оценку! 🙂 Хорошо"
Создается новая запись в БД
```

### **Визуальные индикаторы:**
- ✅ Подсветка текущей оценки при загрузке страницы
- ✅ Анимация при выборе новой оценки
- ✅ Обновление статусного сообщения
- ✅ Toast уведомления с разными сообщениями

## База данных

### **Структура записей:**
```sql
-- День 1
user_id: 1, rating: 5, created_at: '2024-12-30 10:00:00', updated_at: '2024-12-30 10:00:00'

-- День 1 (обновление)
user_id: 1, rating: 3, created_at: '2024-12-30 10:00:00', updated_at: '2024-12-30 15:30:00'

-- День 2 (новая запись)
user_id: 1, rating: 4, created_at: '2024-12-31 09:15:00', updated_at: '2024-12-31 09:15:00'
```

### **Логика определения дня:**
```php
$today = now()->startOfDay();        // 2024-12-30 00:00:00
$tomorrow = now()->addDay()->startOfDay(); // 2024-12-31 00:00:00

// Поиск записи за сегодня
->where('created_at', '>=', $today)
->where('created_at', '<', $tomorrow)
```

## Логирование

### **Расширенное логирование:**
```php
Log::info('User rating submitted', [
    'user_id' => $user->id,
    'uuid' => $user->uuid,
    'tg_id' => $user->tg_id,
    'rating' => $request->rating,
    'emoji' => $emoji,
    'label' => $label,
    'is_update' => $isUpdate,  // Новое поле
    'ip' => $request->ip(),
]);
```

### **Примеры логов:**
```
[2024-12-30 10:00:00] User rating submitted: user_id=1, rating=5, is_update=false
[2024-12-30 15:30:00] User rating submitted: user_id=1, rating=3, is_update=true
[2024-12-31 09:15:00] User rating submitted: user_id=1, rating=4, is_update=false
```

## API Response

### **Новые поля в ответе:**
```json
{
    "success": true,
    "message": "Оценка обновлена! 😐 Нормально",
    "data": {
        "rating": 3,
        "emoji": "😐",
        "label": "Нормально",
        "is_update": true
    }
}
```

### **Типы сообщений:**
- `"Спасибо за оценку! {emoji} {label}"` - первая оценка дня
- `"Оценка обновлена! {emoji} {label}"` - изменение оценки

## Статистика

### **Влияние на статистику:**
- ✅ Каждый день учитывается только последняя оценка пользователя
- ✅ Обновления в течение дня не создают дубликатов
- ✅ Статистика остается корректной
- ✅ Временные метки показывают историю изменений

### **Пример запроса статистики:**
```sql
-- Получить последние оценки пользователей по дням
SELECT user_id, DATE(created_at) as date, rating, updated_at
FROM user_ratings 
ORDER BY user_id, created_at DESC;
```

## Обратная совместимость

### **Сохранена совместимость:**
- ✅ Существующие записи остаются без изменений
- ✅ API endpoints не изменились
- ✅ Структура БД не изменилась
- ✅ Методы статистики работают корректно

### **Миграция не требуется:**
- ✅ Используется существующая таблица user_ratings
- ✅ Новая логика работает с текущими данными
- ✅ Старые записи обрабатываются корректно

Функциональность обновлена и готова к использованию!
