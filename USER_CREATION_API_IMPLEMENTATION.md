# Реализация API для создания пользователей

## Что реализовано

### **1. Новый API Endpoint**
**URL:** `POST /api/user/create`

**Параметры:**
- `tg_id` (string, required) - Telegram ID пользователя
- `comment` (string, required) - Комментарий пользователя (макс. 500 символов)
- `demo` (boolean, optional) - Флаг демо доступа (по умолчанию: false)

### **2. Логика создания пользователя**

#### **Валидация:**
- Проверка обязательных полей (`tg_id`, `comment`)
- Проверка максимальной длины полей
- Проверка существования пользователя по `tg_id` или `email`

#### **Создание пользователя:**
```php
$user = User::create([
    'uuid' => Str::uuid()->toString(),
    'tg_id' => $tgId,
    'email' => "client{$tgId}",
]);
```

#### **Создание XUI клиентов:**
1. Получение всех доступных серверов и inbound'ов
2. Создание записи `XuiClient` для каждого inbound'а
3. Отправка запроса на X-UI сервер для создания клиента

### **3. Интеграция с X-UI серверами**

#### **Новый метод в XuiApiService:**
```php
public function createClient(XuiServer $server, XuiInbound $inbound, $user, string $comment, bool $isDemo = false): bool
```

#### **Параметры запроса к X-UI:**
```php
POST https://{xui_host}:{xui_port}/{xui_path}/panel/inbound/addClient

// Form data:
id => {$inbound_id}
settings => {
    "clients": [{
        "id": "{$user->uuid}",
        "flow": "xtls-rprx-vision",
        "email": "{$user->email}",
        "limitIp": 0,
        "totalGB": 0, // или 1GB для demo
        "expiryTime": {$expirationTimestamp},
        "enable": true,
        "tgId": "",
        "subId": "unlimited_{$tgId}",
        "comment": "{$comment}",
        "reset": 0
    }]
}
```

#### **Различия для demo пользователей:**
- **Срок действия**: 1 день (вместо 30 дней)
- **Лимит трафика**: 1GB (вместо безлимита)

## Примеры использования

### **1. Создание обычного пользователя:**
```bash
curl -X POST "https://domain.com/api/user/create" \
     -H "Content-Type: application/json" \
     -d '{
       "tg_id": "123456789",
       "comment": "Новый пользователь VPN",
       "demo": false
     }'
```

### **2. Создание demo пользователя:**
```bash
curl -X POST "https://domain.com/api/user/create" \
     -H "Content-Type: application/json" \
     -d '{
       "tg_id": "987654321", 
       "comment": "Демо доступ на 1 день",
       "demo": true
     }'
```

### **3. Ответ при успешном создании:**
```json
{
    "success": true,
    "message": "User created successfully",
    "data": {
        "user": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "email": "client123456789",
            "tg_id": "123456789",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        },
        "subscription_link": "https://domain.com/subs/550e8400-e29b-41d4-a716-************/modern",
        "clients_created": 3,
        "clients_failed": 0,
        "created_clients": [
            {
                "server": "Server 1",
                "inbound_id": 1,
                "xui_client_id": 1
            }
        ],
        "failed_clients": [],
        "is_demo": false
    }
}
```

## Обработка ошибок

### **1. Пользователь уже существует (409):**
```json
{
    "success": false,
    "message": "User already exists",
    "data": {
        "existing_user": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "email": "client123456789",
            "tg_id": "123456789"
        }
    }
}
```

### **2. Ошибка валидации (400):**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "tg_id": ["The tg id field is required."],
        "comment": ["The comment field is required."]
    }
}
```

### **3. Серверная ошибка (500):**
```json
{
    "success": false,
    "message": "Internal server error"
}
```

## Логирование

### **Успешное создание:**
```
[INFO] User created
{
    "user_id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "tg_id": "123456789",
    "email": "client123456789",
    "is_demo": false
}

[INFO] Successfully created client on X-UI server
{
    "server": "Server 1",
    "inbound_id": 1,
    "user_uuid": "550e8400-e29b-41d4-a716-************",
    "user_email": "client123456789",
    "is_demo": false
}
```

### **Ошибки создания клиентов:**
```
[ERROR] Failed to create client on X-UI server
{
    "server": "Server 1",
    "inbound_id": 1,
    "status": 500,
    "response": "Internal server error"
}
```

## Транзакции

### **Использование DB транзакций:**
```php
DB::beginTransaction();
try {
    // Создание пользователя
    $user = User::create([...]);
    
    // Создание XUI клиентов
    foreach ($servers as $server) {
        // Создание записей и API вызовы
    }
    
    DB::commit();
} catch (\Exception $e) {
    DB::rollBack();
    throw $e;
}
```

## Обновленные файлы

### **1. app/Http/Controllers/Api/SubscriptionApiController.php**
- Добавлен метод `createUser()`
- Обновлен метод `info()` с новым endpoint'ом

### **2. app/Services/XuiApiService.php**
- Добавлен метод `createClient()`
- Добавлен импорт `ServerStatusService`

### **3. routes/api.php**
- Добавлен маршрут `POST /api/user/create`

### **4. tests/Feature/SubscriptionApiTest.php**
- Добавлены тесты для создания пользователей
- Тесты валидации и обработки ошибок

### **5. API_DOCUMENTATION.md**
- Обновлена документация с новым endpoint'ом
- Добавлены примеры использования

## Тестирование

### **Запуск тестов:**
```bash
php artisan test tests/Feature/SubscriptionApiTest.php
```

### **Покрытие тестами:**
- ✅ Создание обычного пользователя
- ✅ Создание demo пользователя  
- ✅ Проверка существующего пользователя
- ✅ Валидация обязательных полей
- ✅ Валидация максимальной длины полей

## Мониторинг

### **Метрики для отслеживания:**
- Количество созданных пользователей
- Соотношение demo/обычных пользователей
- Успешность создания клиентов на серверах
- Время выполнения операции создания

### **Логи для анализа:**
```bash
# Созданные пользователи
grep "User created" storage/logs/laravel.log

# Успешные создания клиентов
grep "Successfully created client" storage/logs/laravel.log

# Ошибки создания клиентов
grep "Failed to create client" storage/logs/laravel.log
```

Теперь у вас есть полнофункциональный API для создания пользователей с автоматическим созданием клиентов на всех доступных X-UI серверах!
