<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Подписка</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- React 18 -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- QR Code generator -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.4/lib/browser.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Headless UI (simplified version without CDN) -->
    <style>
        .disclosure-panel {
            transition: all 0.2s ease-in-out;
        }
        .disclosure-panel[data-open="false"] {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
        }
        .disclosure-panel[data-open="true"] {
            max-height: 1000px;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Data from Laravel
        const subscriptionData = {
            email: "{{ $data['email'] }}",
            subscriptionUrl: "{{ $data['subscription_url'] }}",
            vlessLinks: @json($data['vless_links']),
            vlessContent: `{{ str_replace(["\n", "\r"], ["\\n", "\\r"], $data['vless_content']) }}`,
            status: "{{ $data['status'] }}",
            expiryTime: "{{ $data['expiry_time'] ? $data['expiry_time']->format('Y-m-d H:i:s') : '' }}",
            remainingTime: @json($data['remaining_time']),
            enable: {{ $data['enable'] ? 'true' : 'false' }},
            uuid: "{{ $data['uuid'] }}"
        };

        const App = () => {
            const [platform, setPlatform] = useState('');
            const [copied, setCopied] = useState('');
            const [showQR, setShowQR] = useState(false);
            const [qrDataUrl, setQrDataUrl] = useState('');
            const [isGeneratingQR, setIsGeneratingQR] = useState(false);
            const [isMobile, setIsMobile] = useState(false);
            const [isAccordionOpen, setIsAccordionOpen] = useState(false);

            useEffect(() => {
                // Platform detection
                const userAgent = navigator.userAgent.toLowerCase();
                const isMobileDevice = /iphone|ipad|ipod|android|blackberry|mini|windows\sce|palm/i.test(userAgent);
                setIsMobile(isMobileDevice);

                if (/iphone|ipad|ipod/.test(userAgent)) {
                    setPlatform('ios');
                } else if (/android/.test(userAgent)) {
                    setPlatform('android');
                } else if (/win/.test(userAgent)) {
                    setPlatform('windows');
                } else if (/mac/.test(userAgent)) {
                    setPlatform('macos');
                } else {
                    setPlatform('desktop');
                }
            }, []);

            const getPlatformLink = () => {
                switch (platform) {
                    case 'ios':
                    case 'ipad':
                    case 'macos':
                        return 'https://apps.apple.com/ru/app/v2raytun/id6476628951';
                    case 'android':
                        return 'https://play.google.com/store/apps/details?id=com.v2raytun.android';
                    case 'windows':
                        return 'https://storage.v2raytun.com/v2RayTun_Setup.exe';
                    default:
                        return '#';
                }
            };

            const copyToClipboard = async (text, type) => {
                try {
                    await navigator.clipboard.writeText(text);
                    setCopied(type);
                    setTimeout(() => setCopied(''), 2000);
                } catch (err) {
                    console.error('Failed to copy: ', err);
                }
            };

            const generateQRCode = async () => {
                if (isGeneratingQR) return;

                setIsGeneratingQR(true);
                try {
                    const qrDataUrl = await QRCode.toDataURL(subscriptionData.subscriptionUrl, {
                        width: 256,
                        margin: 2,
                        color: {
                            dark: '#000000',
                            light: '#FFFFFF'
                        }
                    });
                    setQrDataUrl(qrDataUrl);
                    setShowQR(true);
                } catch (err) {
                    console.error('Failed to generate QR code: ', err);
                } finally {
                    setIsGeneratingQR(false);
                }
            };

            const getStatusColor = () => {
                switch (subscriptionData.status) {
                    case 'active':
                        return 'text-green-400';
                    case 'expired':
                        return 'text-red-400';
                    case 'limited':
                        return 'text-yellow-400';
                    default:
                        return 'text-gray-400';
                }
            };

            const getStatusText = () => {
                switch (subscriptionData.status) {
                    case 'active':
                        return 'Активна';
                    case 'expired':
                        return 'Истекла';
                    case 'limited':
                        return 'Лимит исчерпан';
                    default:
                        return 'Неизвестно';
                }
            };

            const formatExpiryDate = () => {
                if (!subscriptionData.expiryTime) return 'Без ограничений';
                const date = new Date(subscriptionData.expiryTime);
                return date.toLocaleDateString('ru-RU', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            };

            const StepItem = ({ number, text }) => (
                <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                        <span className="text-white font-bold text-sm">{number}</span>
                    </div>
                    <p className="text-sm text-gray-300">{text}</p>
                </div>
            );

            return (
                <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center p-4">
                    <div className="max-w-md w-full bg-gray-800 rounded-2xl shadow-2xl border border-gray-700">
                        <div className="flex flex-col items-center p-6 space-y-6">
                            {/* Logo/Avatar */}
                            <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i className="fas fa-shield-alt text-white text-3xl"></i>
                            </div>

                            {/* Status */}
                            <div className="text-center">
                                <p className={`text-sm font-medium ${getStatusColor()}`}>
                                    Подписка {getStatusText().toLowerCase()}
                                    {subscriptionData.expiryTime && subscriptionData.status !== 'expired' && ` до ${formatExpiryDate()}`}
                                    {subscriptionData.expiryTime && subscriptionData.status === 'expired' && ` ${formatExpiryDate()}`}
                                </p>
                                {subscriptionData.remainingTime && subscriptionData.remainingTime.display && (
                                    <p className="text-sm text-gray-400 mt-1">
                                        {subscriptionData.status === 'active' ? `осталось ${subscriptionData.remainingTime.display}` : ''}
                                    </p>
                                )}
                            </div>

                            {/* Title */}
                            <h1 className="text-2xl font-bold text-center text-white">
                                Давайте настроим VPN
                            </h1>

                            {/* Steps */}
                            <div className="space-y-4 w-full">
                                <StepItem
                                    number={1}
                                    text={<>Установите приложение, <span className="text-gray-400">но не открывайте его</span></>}
                                />
                                <StepItem
                                    number={2}
                                    text={<>Вернитесь из <span className="text-blue-400">магазина приложений</span> обратно на эту страницу</>}
                                />
                                <StepItem
                                    number={3}
                                    text={<>Нажмите на кнопку <span className="text-green-400">Завершить настройку</span></>}
                                />
                            </div>

                            {/* Install Button */}
                            <a
                                href={getPlatformLink()}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-300 text-center block"
                            >
                                <i className="fas fa-download mr-2"></i>
                                Установить приложение
                            </a>

                            {/* Setup Button */}
                            <button
                                onClick={() => copyToClipboard(subscriptionData.subscriptionUrl, 'setup')}
                                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-300"
                            >
                                <i className="fas fa-cog mr-2"></i>
                                {copied === 'setup' ? 'Скопировано!' : 'Завершить настройку'}
                            </button>

                            {/* Additional Options */}
                            <div className="w-full">
                                <button
                                    onClick={() => setIsAccordionOpen(!isAccordionOpen)}
                                    className="flex justify-between w-full px-4 py-2 text-sm font-medium text-left text-gray-300 bg-gray-700 rounded-lg hover:bg-gray-600 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75"
                                >
                                    <span>Дополнительно</span>
                                    <i className={`fas fa-chevron-${isAccordionOpen ? 'up' : 'down'} text-gray-400`}></i>
                                </button>
                                <div
                                    className={`disclosure-panel px-4 pt-4 pb-2 text-sm text-gray-300 space-y-3 ${isAccordionOpen ? '' : 'hidden'}`}
                                    data-open={isAccordionOpen}
                                >
                                            {!isMobile && (
                                                <button
                                                    onClick={generateQRCode}
                                                    disabled={isGeneratingQR}
                                                    className="w-full bg-purple-600 hover:bg-purple-500 disabled:bg-purple-400 text-white py-2 px-4 rounded-lg transition duration-300 text-sm"
                                                >
                                                    {isGeneratingQR ? (
                                                        <>
                                                            <i className="fas fa-spinner fa-spin mr-2"></i>
                                                            Генерация QR кода...
                                                        </>
                                                    ) : (
                                                        <>
                                                            <i className="fas fa-qrcode mr-2"></i>
                                                            Показать QR код для мобильного
                                                        </>
                                                    )}
                                                </button>
                                            )}

                                            {showQR && qrDataUrl && (
                                                <div className="flex flex-col items-center space-y-2 p-4 bg-white rounded-lg">
                                                    <img src={qrDataUrl} alt="QR Code" className="w-48 h-48" />
                                                    <p className="text-xs text-gray-600 text-center">
                                                        Отсканируйте QR код в приложении для быстрой настройки
                                                    </p>
                                                    <button
                                                        onClick={() => setShowQR(false)}
                                                        className="text-gray-500 hover:text-gray-700 text-xs"
                                                    >
                                                        Скрыть QR код
                                                    </button>
                                                </div>
                                            )}

                                            <button
                                                onClick={() => copyToClipboard(subscriptionData.subscriptionUrl, 'subscription')}
                                                className="w-full bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm"
                                            >
                                                <i className="fas fa-link mr-2"></i>
                                                {copied === 'subscription' ? 'Скопировано!' : 'Скопировать ссылку на подписку'}
                                            </button>
                                            <button
                                                onClick={() => copyToClipboard(subscriptionData.vlessContent, 'vless')}
                                                className="w-full bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm"
                                            >
                                                <i className="fas fa-code mr-2"></i>
                                                {copied === 'vless' ? 'Скопировано!' : 'Скопировать конфигурации vless'}
                                            </button>
                                            <a
                                                href={`/subs/${subscriptionData.uuid}`}
                                                className="w-full bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm block text-center"
                                            >
                                                <i className="fas fa-eye mr-2"></i>
                                                Классический вид
                                            </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
