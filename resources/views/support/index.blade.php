<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Поддержка - SmartVPN</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        #logo {
            background-image: url('/storage/images/smartvpn-logo.jpg');
            background-size: cover;
            background-position: center;
        }

        .mood-button {
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            user-select: none;
        }

        .mood-button:hover {
            transform: scale(1.1);
        }

        .mood-button.selected {
            transform: scale(1.25);
            filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.5));
        }

        .mood-button:active {
            transform: scale(0.95);
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
            padding: 16px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .toast.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .toast.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .support-button {
            transition: all 0.2s ease-in-out;
        }

        .support-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .support-button:active {
            transform: translateY(0);
        }

        @media (max-width: 640px) {
            .toast {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }
    </style>
</head>
<body class="bg-gray-900 min-h-screen">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-md w-full bg-gray-800 rounded-2xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-center">
                <div id="logo" class="w-20 h-20 rounded-full mx-auto mb-4 border-4 border-white shadow-lg"></div>
                <h1 class="text-2xl font-bold text-white mb-2">SmartVPN</h1>
                <p class="text-blue-100">
                    <i class="fas fa-id-card mr-2"></i>ID: {{ $user->tg_id ?? $user->uuid }}
                </p>
            </div>

            <div class="p-6 space-y-6">
                <!-- Rating System -->
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-white mb-3">Оцените наш сервис</h3>
                    <div class="flex justify-center space-x-3 mb-4" id="ratingContainer">
                        <button class="mood-button text-3xl" data-rating="1" data-emoji="😡" data-label="Очень плохо" title="Очень плохо">😡</button>
                        <button class="mood-button text-3xl" data-rating="2" data-emoji="😕" data-label="Плохо" title="Плохо">😕</button>
                        <button class="mood-button text-3xl" data-rating="3" data-emoji="😐" data-label="Нормально" title="Нормально">😐</button>
                        <button class="mood-button text-3xl" data-rating="4" data-emoji="🙂" data-label="Хорошо" title="Хорошо">🙂</button>
                        <button class="mood-button text-3xl" data-rating="5" data-emoji="😄" data-label="Отлично" title="Отлично">😄</button>
                    </div>
                    @if(!$canRate)
                        <p class="text-sm text-yellow-400">
                            <i class="fas fa-clock mr-1"></i>
                            Вы уже оценили сервис сегодня
                        </p>
                    @endif
                </div>

                <!-- Subscription Status -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-white mb-3">
                        <i class="fas fa-shield-alt mr-2"></i>Статус подписки
                    </h2>
                    <div class="space-y-2">
                        <p class="{{ $statusColor }} font-semibold">{{ $status }}</p>
                        @if($user->expiry_time)
                            <p class="text-sm text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>
                                до {{ $user->expiry_time->format('d.m.Y H:i') }}
                            </p>
                            @if($daysUntilExpiry !== null)
                                <p class="text-sm text-gray-400">
                                    <i class="fas fa-hourglass-half mr-1"></i>
                                    осталось {{ $daysUntilExpiry }} {{ $daysUntilExpiry == 1 ? 'день' : ($daysUntilExpiry < 5 ? 'дня' : 'дней') }}
                                </p>
                            @endif
                        @endif
                        @if($isDemo)
                            <p class="text-sm text-yellow-400">
                                <i class="fas fa-star mr-1"></i>Демо версия
                            </p>
                        @endif
                    </div>
                </div>

                <!-- Support Buttons -->
                <div class="space-y-3">
                    <a href="{{ $telegramSupport }}"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="support-button flex items-center justify-center w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg">
                        <i class="fab fa-telegram-plane mr-3 text-xl"></i>
                        Техподдержка в Telegram
                    </a>

                    <a href="{{ $whatsappSupport }}"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="support-button flex items-center justify-center w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg">
                        <i class="fab fa-whatsapp mr-3 text-xl"></i>
                        Техподдержка в WhatsApp
                    </a>

                    <a href="{{ $telegramChannel }}"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="support-button flex items-center justify-center w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-4 rounded-lg">
                        <i class="fas fa-users mr-3 text-xl"></i>
                        Наш канал в Telegram
                    </a>

                    <a href="{{ route('subscription.modern.simple', $user->uuid) }}"
                       class="support-button flex items-center justify-center w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg">
                        <i class="fas fa-arrow-left mr-3 text-xl"></i>
                        Вернуться к подписке
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <script>
        // Rating system
        const ratingButtons = document.querySelectorAll('.mood-button');
        const canRate = {{ $canRate ? 'true' : 'false' }};
        const userUuid = '{{ $user->uuid }}';

        // Initialize rating buttons
        ratingButtons.forEach(button => {
            if (!canRate) {
                button.style.opacity = '0.5';
                button.style.cursor = 'not-allowed';
                return;
            }

            button.addEventListener('click', function() {
                const rating = this.dataset.rating;
                const emoji = this.dataset.emoji;
                const label = this.dataset.label;

                // Visual feedback
                ratingButtons.forEach(btn => btn.classList.remove('selected'));
                this.classList.add('selected');

                // Submit rating
                submitRating(rating, emoji, label);
            });
        });

        // Submit rating function
        async function submitRating(rating, emoji, label) {
            try {
                const response = await fetch('{{ route("support.rating") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        uuid: userUuid,
                        rating: parseInt(rating)
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');

                    // Disable rating buttons
                    ratingButtons.forEach(btn => {
                        btn.style.opacity = '0.5';
                        btn.style.cursor = 'not-allowed';
                        btn.onclick = null;
                    });

                    // Update can rate status
                    const ratingContainer = document.getElementById('ratingContainer');
                    const existingMessage = ratingContainer.parentNode.querySelector('.text-yellow-400');
                    if (!existingMessage) {
                        const message = document.createElement('p');
                        message.className = 'text-sm text-yellow-400';
                        message.innerHTML = '<i class="fas fa-clock mr-1"></i>Вы уже оценили сервис сегодня';
                        ratingContainer.parentNode.appendChild(message);
                    }
                } else {
                    showToast(data.message || 'Ошибка при отправке оценки', 'error');
                }
            } catch (error) {
                console.error('Rating submission error:', error);
                showToast('Ошибка при отправке оценки', 'error');
            }
        }

        // Toast notification function
        function showToast(message, type = 'success', duration = 5000) {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-3"></i>
                    <span>${message}</span>
                </div>
            `;

            document.getElementById('toastContainer').appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Hide and remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states to support buttons
            const supportButtons = document.querySelectorAll('.support-button');
            supportButtons.forEach(button => {
                if (button.href && button.href.startsWith('http')) {
                    button.addEventListener('click', function() {
                        const icon = this.querySelector('i');
                        const originalClass = icon.className;
                        icon.className = 'fas fa-spinner fa-spin mr-3 text-xl';

                        setTimeout(() => {
                            icon.className = originalClass;
                        }, 1000);
                    });
                }
            });
        });
    </script>
</body>
</html>
