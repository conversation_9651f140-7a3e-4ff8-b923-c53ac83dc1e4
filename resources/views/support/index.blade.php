import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Button } from "@heroui/react";
import { Icon } from "@iconify/react";

const App: React.FC = () => {
  const [selectedMood, setSelectedMood] = React.useState<number | null>(null);

  const moods = [
    { emoji: "😡", label: "Angry" },
    { emoji: "😕", label: "Confused" },
    { emoji: "😐", label: "Neutral" },
    { emoji: "🙂", label: "Happy" },
    { emoji: "😄", label: "Excited" }
  ];

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <Card className="max-w-md w-full bg-gray-800 text-white">
        <div className="flex flex-col items-center p-6 space-y-6">
          <Avatar
            src="https://img.heroui.chat/image/avatar?w=200&h=200&u=1"
            className="w-24 h-24"
            alt="User avatar"
          />

          <div className="text-center">
            <h1 className="text-2xl font-bold">
                SmartVPN
            </h1>
            <p className="text-gray-400">
                🔰 ID клиента: 1234567890
            </p>
          </div>

          {/* New mood rating system */}
          <div className="flex justify-center space-x-2">
            {moods.map((mood, index) => (
              <button
                key={index}
                className={`text-2xl ${selectedMood === index ? 'transform scale-125' : ''} transition-transform duration-200 ease-in-out focus:outline-none`}
                onClick={() => setSelectedMood(index)}
                aria-label={mood.label}
              >
                {mood.emoji}
              </button>
            ))}
          </div>

          <div className="bg-gray-700 rounded-lg p-4 w-full">
            <h2 className="text-xl font-semibold mb-2">Статус подписки</h2>
            <p className="text-green-400">Активна</p>
            <p className="text-sm text-gray-400">до 2025-09-22 13:33</p>
            <p className="text-sm text-gray-400">осталось 54 дня</p>
          </div>

          <div className="w-full space-y-4">
            <Button
              color="primary"
              className="w-full"
              size="lg"
              as="a"
              href="https://t.me/support_bot"
              target="_blank"
              rel="noopener noreferrer"
              startContent={<Icon icon="logos:telegram" />}
            >
              Техподдержка в Telegram
            </Button>

            <Button
              color="success"
              className="w-full"
              size="lg"
              as="a"
              href="https://t.me/1234567890"
              target="_blank"
              rel="noopener noreferrer"
              startContent={<Icon icon="logos:whatsapp" />}
            >
              Техподдержка в WhatsApp
            </Button>

            <Button
              color="warning"
              className="w-full"
              size="lg"
              as="a"
              href="https://t.me/support_bot?start=ref_1234567890"
              target="_blank"
              rel="noopener noreferrer"
              startContent={<Icon icon="logos:people" />}
            >
              Пригласить друзей
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default App;
