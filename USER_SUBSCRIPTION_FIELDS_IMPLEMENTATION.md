# Добавление полей подписки в таблицу users

## Обзор изменений

### **Новые поля в таблице users:**
- `comment` (text, nullable) - Комментарий пользователя
- `expiry_time` (timestamp, nullable) - Время истечения подписки
- `expired` (boolean, default: false) - Флаг истечения подписки
- `disabled_at` (timestamp, nullable) - Время отключения пользователя
- `total_gb` (bigint, default: 0) - Лимит трафика в байтах
- `used_gb` (bigint, default: 0) - Использованный трафик в байтах
- `up_traffic` (bigint, default: 0) - Исходящий трафик в байтах
- `down_traffic` (bigint, default: 0) - Входящий трафик в байтах

## Реализация

### **1. Миграция базы данных**
**Файл:** `database/migrations/2024_12_30_000004_add_subscription_fields_to_users_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->text('comment')->nullable()->after('demo_until');
            $table->timestamp('expiry_time')->nullable()->after('comment');
            $table->boolean('expired')->default(false)->after('expiry_time');
            $table->timestamp('disabled_at')->nullable()->after('expired');
            $table->bigInteger('total_gb')->default(0)->after('disabled_at')->comment('Total traffic limit in bytes');
            $table->bigInteger('used_gb')->default(0)->after('total_gb')->comment('Used traffic in bytes');
            $table->bigInteger('up_traffic')->default(0)->after('used_gb')->comment('Upload traffic in bytes');
            $table->bigInteger('down_traffic')->default(0)->after('up_traffic')->comment('Download traffic in bytes');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'comment', 'expiry_time', 'expired', 'disabled_at',
                'total_gb', 'used_gb', 'up_traffic', 'down_traffic'
            ]);
        });
    }
};
```

### **2. Обновление модели User**
**Файл:** `app/Models/User.php`

#### **Добавлены в fillable:**
```php
protected $fillable = [
    'uuid', 'name', 'email', 'tg_id', 'demo_until',
    'comment', 'expiry_time', 'expired', 'disabled_at',
    'total_gb', 'used_gb', 'up_traffic', 'down_traffic',
    'password',
];
```

#### **Добавлены в casts:**
```php
protected function casts(): array
{
    return [
        'email_verified_at' => 'datetime',
        'demo_until' => 'datetime',
        'expiry_time' => 'datetime',
        'expired' => 'boolean',
        'disabled_at' => 'datetime',
        'total_gb' => 'integer',
        'used_gb' => 'integer',
        'up_traffic' => 'integer',
        'down_traffic' => 'integer',
        'password' => 'hashed',
    ];
}
```

### **3. Обновление API контроллера**
**Файл:** `app/Http/Controllers/Api/SubscriptionApiController.php`

#### **Расчет времени истечения:**
```php
// Calculate expiry_time
$expiryTime = null;
if ($expiration) {
    $expiryTime = $expiration > 9999999999
        ? \Carbon\Carbon::createFromTimestampMs($expiration)
        : \Carbon\Carbon::createFromTimestamp($expiration);
} else {
    // Default: demo: 1 day, regular: 30 days
    $expiryDays = $isDemo ? 1 : 30;
    $expiryTime = now()->addDays($expiryDays);
}
```

#### **Создание пользователя с новыми полями:**
```php
$userData = [
    'uuid' => Str::uuid()->toString(),
    'tg_id' => $tgId,
    'email' => $email,
    'comment' => $comment,
    'expiry_time' => $expiryTime,
    'expired' => false,
    'disabled_at' => null,
    'total_gb' => $isDemo ? 1073741824 : 0, // 1GB for demo, unlimited for regular
    'used_gb' => 0,
    'up_traffic' => 0,
    'down_traffic' => 0,
];
```

#### **Создание XuiClient с данными из User:**
```php
$xuiClient = XuiClient::create([
    'user_id' => $user->id,
    'xui_server_id' => $server->id,
    'xui_inbound_id' => $inbound->id,
    'client_id' => $user->uuid,
    'email' => $clientEmail,
    'enable' => true,
    'disabled_at' => $user->disabled_at,
    'tg_id' => $tgId,
    'sub_id' => "unlimited_{$tgId}",
    'comment' => $user->comment,
    'expiry_time' => $user->expiry_time,
    'expired' => $user->expired,
    'total_gb' => $user->total_gb,
    'used_gb' => $user->used_gb,
    'up_traffic' => $user->up_traffic,
    'down_traffic' => $user->down_traffic,
]);
```

### **4. Обновление XuiApiService**
**Файл:** `app/Services/XuiApiService.php`

#### **Использование данных из User модели:**
```php
// Prepare client data using User model data
$clientData = [
    'id' => $user->uuid,
    'flow' => 'xtls-rprx-vision',
    'email' => $clientEmail,
    'limitIp' => 0,
    'totalGB' => $user->total_gb,
    'expiryTime' => $user->expiry_time ? $user->expiry_time->timestamp * 1000 : $expirationTimestamp,
    'enable' => !$user->expired && !$user->disabled_at,
    'tgId' => '',
    'subId' => "unlimited_{$user->tg_id}",
    'comment' => $user->comment,
    'reset' => 0
];
```

### **5. Обновление SubscriptionService**
**Файл:** `app/Services/SubscriptionService.php`

#### **Добавлен вызов обновления статистики:**
```php
$this->updateClientRecord($user, $server, $inbound, $clientData, $trafficStats);
$this->updateUserTrafficStats($user); // Новый вызов
$processedCount++;
```

#### **Новый метод updateUserTrafficStats:**
```php
private function updateUserTrafficStats(User $user): void
{
    try {
        // Sum traffic from all user's clients
        $trafficStats = $user->xuiClients()
            ->selectRaw('
                SUM(up_traffic) as total_up_traffic,
                SUM(down_traffic) as total_down_traffic,
                SUM(used_gb) as total_used_gb
            ')
            ->first();

        // Update user with aggregated traffic stats
        $user->update([
            'up_traffic' => $trafficStats->total_up_traffic ?? 0,
            'down_traffic' => $trafficStats->total_down_traffic ?? 0,
            'used_gb' => $trafficStats->total_used_gb ?? 0,
        ]);

        Log::debug("Updated user traffic stats", [
            'user_id' => $user->id,
            'up_traffic' => $trafficStats->total_up_traffic ?? 0,
            'down_traffic' => $trafficStats->total_down_traffic ?? 0,
            'used_gb' => $trafficStats->total_used_gb ?? 0,
        ]);
    } catch (\Exception $e) {
        Log::error("Failed to update user traffic stats", [
            'user_id' => $user->id,
            'error' => $e->getMessage(),
        ]);
    }
}
```

## Логика работы

### **1. При создании пользователя:**
1. **Расчет expiry_time** - на основе параметра `expiration` или дефолтных значений
2. **Заполнение полей** - comment, expiry_time, expired, disabled_at, total_gb, used_gb, up_traffic, down_traffic
3. **Передача в XuiClient** - все поля копируются из User в XuiClient
4. **Передача в X-UI API** - данные из User используются для создания клиентов

### **2. При синхронизации (SyncSubscriptionsJob):**
1. **Обновление XuiClient** - статистика обновляется из X-UI API
2. **Агрегация в User** - up_traffic, down_traffic, used_gb суммируются из всех XuiClient'ов
3. **Логирование** - отслеживание изменений статистики

### **3. Поля и их назначение:**

#### **Основные поля:**
- `comment` - комментарий пользователя (передается в X-UI)
- `expiry_time` - время истечения подписки (передается в X-UI)
- `expired` - флаг истечения (рассчитывается автоматически)
- `disabled_at` - время отключения (управляется вручную)

#### **Трафик:**
- `total_gb` - лимит трафика (передается в X-UI как totalGB)
- `used_gb` - использованный трафик (агрегируется из всех клиентов)
- `up_traffic` - исходящий трафик (агрегируется из всех клиентов)
- `down_traffic` - входящий трафик (агрегируется из всех клиентов)

## Примеры данных

### **Создание пользователя:**
```json
{
    "tg_id": "100008",
    "comment": "VIP пользователь",
    "demo": false,
    "expiration": 1735689600
}
```

### **Результат в таблице users:**
```sql
INSERT INTO users (
    uuid, tg_id, email, comment, expiry_time, expired, disabled_at,
    total_gb, used_gb, up_traffic, down_traffic
) VALUES (
    'uuid-here', '100008', 'client100008', 'VIP пользователь',
    '2024-01-01 00:00:00', false, null,
    0, 0, 0, 0
);
```

### **После синхронизации:**
```sql
UPDATE users SET
    used_gb = 524288000,      -- 500MB
    up_traffic = 262144000,   -- 250MB
    down_traffic = 262144000  -- 250MB
WHERE id = 70;
```

## Преимущества

### **1. Централизация данных:**
- ✅ Все данные пользователя в одном месте
- ✅ Единый источник истины для лимитов и статистики
- ✅ Простые запросы для получения информации о пользователе

### **2. Консистентность:**
- ✅ Одинаковые данные во всех XuiClient'ах пользователя
- ✅ Автоматическая синхронизация статистики
- ✅ Единые правила для всех серверов

### **3. Производительность:**
- ✅ Агрегированная статистика в таблице users
- ✅ Быстрые запросы без JOIN'ов
- ✅ Эффективное обновление данных

### **4. Масштабируемость:**
- ✅ Готовность к добавлению новых серверов
- ✅ Поддержка множественных inbound'ов
- ✅ Гибкая система лимитов и ограничений

Теперь система имеет полную поддержку полей подписки на уровне пользователей!
