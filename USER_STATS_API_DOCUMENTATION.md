# API Endpoint: Статистика пользователя

## Endpoint
```
GET /api/user/stats
```

## Описание
Возвращает подробную статистику пользователя по одному из идентификаторов: `tg_id`, `uuid`, или `email`.

## Параметры запроса

### Query Parameters (один обязателен):
- `tg_id` (string, optional) - Telegram ID пользователя
- `uuid` (string, optional) - UUID пользователя  
- `email` (string, optional) - Email пользователя

**Примечание:** Должен быть указан хотя бы один из параметров.

## Примеры запросов

### 1. По Telegram ID:
```bash
curl "http://localhost:8000/api/user/stats?tg_id=100001"
```

### 2. По UUID:
```bash
curl "http://localhost:8000/api/user/stats?uuid=550e8400-e29b-41d4-a716-************"
```

### 3. По Email:
```bash
curl "http://localhost:8000/api/user/stats?email=client100001"
```

## Ответ API

### Успешный ответ (200):
```json
{
    "success": true,
    "data": {
        // Основная информация пользователя
        "tg_id": "100001",
        "uuid": "550e8400-e29b-41d4-a716-************",
        "email": "client100001",
        "comment": "VIP пользователь",
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z",

        // Серверы и подключения
        "servers": {
            "Nederlands": [3, 4],
            "Moldova": [1]
        },
        "enable": true,
        "can_connect": true,

        // Статистика трафика (из таблицы users)
        "up": 4191841897,
        "down": 11131353542,
        "total": 1073741824,
        "used_traffic": 15323195439,
        "remaining_traffic": 0,
        "usage_percentage": 1428.5,
        "daily_average": 765159772,

        // Время и истечение
        "expiryTime": 1770381046552,
        "days_until_expiry": 15,
        "is_expired": false,
        "expired": false,
        "disabled_at": null,

        // Демо и статус
        "is_demo": true,
        "demo_until": "2024-01-02T00:00:00.000000Z",
        "status": "demo",
        "started_using": true,
        "needs_renewal": false,

        // Статистика клиентов
        "total_clients": 3,
        "active_clients": 3,
        "disabled_clients": 0,
        "expired_clients": 0,

        // Дополнительная информация
        "days_since_creation": 20,
        "last_activity": "2024-01-01T12:00:00.000000Z"
    }
}
```

## Описание полей ответа

### **Основная информация:**
- `tg_id` - Telegram ID пользователя
- `uuid` - Уникальный идентификатор пользователя
- `email` - Email пользователя
- `comment` - Комментарий пользователя
- `created_at` - Дата создания пользователя
- `updated_at` - Дата последнего обновления

### **Серверы и подключения:**
- `servers` - Объект с серверами и их inbound ID
- `enable` - Может ли пользователь подключаться (не истек и не отключен)
- `can_connect` - Может ли реально подключиться (есть активные клиенты)

### **Статистика трафика (из таблицы users):**
- `up` - Исходящий трафик в байтах
- `down` - Входящий трафик в байтах
- `total` - Общий лимит трафика в байтах (0 = безлимит)
- `used_traffic` - Использованный трафик (up + down)
- `remaining_traffic` - Остаток трафика (null если безлимит)
- `usage_percentage` - Процент использования от лимита
- `daily_average` - Средний дневной трафик в байтах

### **Время и истечение:**
- `expiryTime` - Время истечения в миллисекундах (Unix timestamp)
- `days_until_expiry` - Дней до истечения (null если безлимит)
- `is_expired` - Истекла ли подписка
- `expired` - Флаг истечения из БД
- `disabled_at` - Время отключения пользователя

### **Демо и статус:**
- `is_demo` - Является ли демо пользователем
- `demo_until` - До какого времени демо
- `status` - Статус: "active", "expired", "disabled", "demo"
- `started_using` - Начал ли использовать (трафик > 0)
- `needs_renewal` - Нужно ли продление (истек или истекает в течение 3 дней)

### **Статистика клиентов:**
- `total_clients` - Общее количество клиентов
- `active_clients` - Активные клиенты
- `disabled_clients` - Отключенные клиенты
- `expired_clients` - Истекшие клиенты

### **Дополнительная информация:**
- `days_since_creation` - Дней с момента создания
- `last_activity` - Последняя активность клиентов

## Возможные статусы

### **status поле:**
- `"active"` - Активный пользователь
- `"demo"` - Демо пользователь
- `"expired"` - Истекший пользователь
- `"disabled"` - Отключенный пользователь

## Ошибки

### 422 - Validation Error:
```json
{
    "success": false,
    "message": "At least one of tg_id, uuid, or email is required"
}
```

### 404 - User Not Found:
```json
{
    "success": false,
    "message": "User not found"
}
```

### 500 - Server Error:
```json
{
    "success": false,
    "message": "Failed to get user stats",
    "error": "Error details"
}
```

## Примеры использования

### 1. Проверка статуса пользователя:
```bash
curl "http://localhost:8000/api/user/stats?tg_id=100001" | jq '.data.status'
# Ответ: "active"
```

### 2. Получение трафика пользователя:
```bash
curl "http://localhost:8000/api/user/stats?uuid=550e8400-e29b-41d4-a716-************" | jq '.data | {up, down, total, usage_percentage}'
```

### 3. Проверка истечения подписки:
```bash
curl "http://localhost:8000/api/user/stats?email=client100001" | jq '.data | {is_expired, days_until_expiry, needs_renewal}'
```

### 4. Получение информации о серверах:
```bash
curl "http://localhost:8000/api/user/stats?tg_id=100001" | jq '.data.servers'
```

## Особенности

### **Источник данных:**
- Все данные по трафику берутся из таблицы `users` (централизованно)
- Информация о серверах и клиентах из связанных таблиц
- Статистика обновляется при синхронизации

### **Производительность:**
- Быстрые запросы без сложной агрегации
- Данные уже подготовлены в таблице users
- Минимальная нагрузка на базу данных

### **Безопасность:**
- Валидация входных параметров
- Логирование запросов
- Обработка ошибок

Этот endpoint предоставляет полную информацию о пользователе для мониторинга, биллинга и управления подписками.
